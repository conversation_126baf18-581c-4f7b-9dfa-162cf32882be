/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#8B4513',
        secondary: '#DAA520',
        accent: '#CD853F',
        neutral: '#FFF8E7',
        dark: '#3E2723',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Playfair Display', 'serif'],
      },
      boxShadow: {
        'elevation-1': '0 2px 5px rgba(0, 0, 0, 0.1)',
        'elevation-2': '0 4px 10px rgba(0, 0, 0, 0.15)',
        'elevation-3': '0 8px 30px rgba(0, 0, 0, 0.2)',
      },
    },
  },
  plugins: [],
}

