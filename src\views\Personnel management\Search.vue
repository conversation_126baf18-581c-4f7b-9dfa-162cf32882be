<template>
  <!-- 人员管理 -->
  <div>
    <!-- 面包屑导航区 -->
    <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-bottom: 20px">
      <el-breadcrumb-item :to="{ path: '/home' }">{{ $t('common.home') }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('employee.nav1')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('employee.nav2')}}</el-breadcrumb-item>
    </el-breadcrumb>

    <!--  多条件查询  -->
    <div style="padding: 10px 0">
      <el-input style="width:250px" suffix-icon="el-icon-search" :placeholder="$t('employee.name')" v-model="employeename"
        clearable></el-input>
      <el-input style="width:250px" suffix-icon="el-icon-phone" :placeholder="$t('employee.mobile')" v-model="phone"
        clearable></el-input>
      <el-input style="width:250px" suffix-icon="el-icon-message" :placeholder="$t('employee.email')" v-model="email"
        clearable></el-input>
      <!-- <el-input style="width:250px" suffix-icon="el-icon-position" placeholder="请输入部门名称" v-model="departmentname"
                clearable></el-input> -->
      <el-button style="margin-left: 3px" type="primary" @click="load">{{ $t('common.search') }}</el-button>
      <el-button style="margin-left: 3px" type="warning" @click="reset">{{ $t('common.reset') }}</el-button>
    </div>
    <div style="margin: 5px 0">
      <el-button type="primary" @click="handleAdd">{{ $t('common.add') }}<i
          class="el-icon-circle-plus-outline"></i></el-button>
      <!-- <el-button type="primary" @click="exp" style="margin-left: 10px">导出<i class="el-icon-download"></i></el-button> -->
      <!-- <el-button type="danger" :disabled="delBtlStatus" @click="deleteBatch(this.multipleSelection)">批量删除<i
          class="el-icon-remove-outline"></i></el-button> -->
    </div>

    <!--  主体数据表格  -->
    <el-table :data="tableData" border v-loading="loadingData" stripe :header-cell-class-name="headerBg"
      @selection-change="handleSelectionChange" :default-sort="{ prop: 'employeeid' }">
    
      <el-table-column prop="employeeid" :label="$t('employee.id')">
      </el-table-column>
      <el-table-column prop="username" :label="$t('common.username')">
      </el-table-column>
      <el-table-column prop="employeename" :label="$t('employee.nick')">
      </el-table-column>
      <el-table-column prop="phone" :label="$t('vci.cell')">
      </el-table-column>
      <el-table-column prop="email" :label="$t('employee.email')" width="150">
      </el-table-column>
      <el-table-column prop="status" :label="$t('employee.status')">
        <template #default="scope">
          <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1"
            @click="updateState(scope.row.employeeid, scope.row.status)" />
        </template>
      </el-table-column>
      <!-- <el-table-column prop="departmentname" label="所属部门">
      </el-table-column> -->
      <el-table-column prop="role" :label="$t('employee.role')">
        <template #default="scope">
          <el-tag size="small" type="success">{{ _flag2name(scope.row.role) }}</el-tag>
          <!-- <el-tag size="small" v-if="scope.row.role === 'ROLE_NORMAL'" type="success">普通用户</el-tag>
          <el-tag size="small" v-else-if="scope.row.role === 'ROLE_ADMIN'" type="danger">管理员</el-tag> -->
        </template>
      </el-table-column>
      <el-table-column fixed="right" :label="$t('common.action')" width="200">
        <template #default="scope">
          <el-button type="warning" size="small"
            @click="handleEdit(scope.row, scope.row.id)">{{ $t('common.revise') }}<el-icon :size="16"
              style="margin-left: 3px;">
              <Edit />
            </el-icon></el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope.row.employeeid)">{{ $t('common.del') }}<el-icon
              :size="16" style="margin-left: 3px;">
              <Delete />
            </el-icon></el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--  分页  -->
    <div style="padding-left: 400px;padding-top: 15px">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNum"
        :page-sizes="[10, 20, 30, 40]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!--编辑弹窗-->
    <!-- <el-dialog :title="$t('employee.edit')" v-model="editFormVisible" width="35%" center top="20px"></el-dialog> -->
    <el-dialog :title="$t('employee.edit')" v-model="editFormVisible" width="45%" center top="20px">
      <el-form label-width="180px" :model="form" ref="form" :rules="editRules">
        <el-form-item :label="$t('employee.nick')" prop="employeename">
          <el-input v-model="form.employeename" autocomplete="off"></el-input>
          <el-alert :closable="false" type="info" style="line-height: 12px;"></el-alert>
        </el-form-item>
        <el-form-item :label="$t('common.username')" prop="username">
          <el-input v-model="form.username" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.email1')" prop="email">
          <el-input v-model="form.email" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.pwd')" prop="password">
          <el-input v-model="form.password" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.mobile1')" prop="phone">
          <el-input v-model="form.phone" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">{{$t("client.enable")}}</el-radio>
            <el-radio :label="1">{{$t("client.disable")}}</el-radio>
            <!-- <el-radio :label="2">审批未通过</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('employee.role')" prop="role">
          <el-select collapse-tags collapse-tags-tooltip :max-collapse-tags="3" style="width: 330px"
            v-model="form.role" :placeholder="$t('common.selRole')">
            <el-option v-for="role in roles" :key="role.id" :label="role.name"
              :value="role.flag"></el-option>
          </el-select>
          <!-- <el-radio-group v-model="form.role">
            <el-radio label="ROLE_NORMAL">普通用户</el-radio>
            <el-radio label="ROLE_ADMIN">管理员</el-radio>
          </el-radio-group> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" style="margin-top: -40px">
          <el-button @click="editFormVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="edit">{{ $t('common.ok') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!--新增弹窗-->
    <el-dialog :title="$t('employee.add')" v-model="addFormVisible" center fullscreen>
      <el-form label-width="180px" :model="form" ref="form" :rules="editRules">
        <el-form-item :label="$t('common.username')" prop="username">
          <el-input v-model="form.username" autocomplete="off"></el-input>
          <el-alert :closable="false" type="info" style="line-height: 12px;"></el-alert>
        </el-form-item>
        <el-form-item :label="$t('employee.nick')" prop="employeename">
          <el-input v-model="form.employeename" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.email1')" prop="email">
          <el-input v-model="form.email" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.pwd')" prop="password">
          <el-input v-model="form.password" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.mobile1')" prop="phone">
          <el-input v-model="form.phone" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('employee.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">{{$t("client.enable")}}</el-radio>
            <el-radio label="1">{{$t("client.disable")}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('employee.role')" prop="role">
          <el-select collapse-tags collapse-tags-tooltip :max-collapse-tags="3" style="width: 330px"
            v-model="form.role" :placeholder="$t('common.selRole')">
            <el-option v-for="role in roles" :key="role.id" :label="role.name"
              :value="role.flag"></el-option>
          </el-select>
          <!-- <el-radio-group v-model="form.role">
            <el-radio label="ROLE_NORMAL">普通用户</el-radio>
            <el-radio label="ROLE_ADMIN">管理员</el-radio>
          </el-radio-group> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" style="margin-top: -40px">
          <el-button @click="addFormVisible = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="save">{{ $t('common.ok') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { flag2name } from '@/util/utils.js'
export default {
  name: "Search",
  data() {
    let validateUserName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("真实姓名不能为空"))
      } else {
        if (value !== "") {
          const realnameReg = /^([a-zA-Z0-9\u4e00-\u9fa5\·]{1,5})$/;
          if (!realnameReg.test(value)) {
            callback(new Error('您的真实姓名格式错误,请输入1—5个英文或汉字!'));
          }
        }
        callback();
      }
    };
    let vaildateNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('电话号码不能为空'));
      } else {
        if (value !== "") {
          var reg = /^1[3456789]\d{9}$/;
          if (!reg.test(value)) {
            callback(new Error('请输入有效的手机号码'));
          }
        }
        callback();
      }
    };
    let validateEmail = (rule, value, callback) => {
      if (value === "") {
        callback(new Error('邮箱不能为空'));
      } else {
        if (value !== "") {
          var regEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
          if (!regEmail.test(value)) {
            callback(new Error('请输入有效的邮箱'));
          }
        }
        callback();
      }
    };

    return {
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      username: '',
      mail: '',
      tel: '',
      departmentname: '',
      form: {},
      multipleSelection: [],
      headerBg: 'headerBg',
      editFormVisible: false,
      addFormVisible: false,
      delBtlStatus: true,
      editRules: {
        employeename: [
          { required: true, validator: validateUserName, trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        phone: [
          { required: true, validator: vaildateNumber, trigger: 'blur' },
        ],
        email: [
          { required: true, validator: validateEmail, trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ],
        departmentid: [
          { required: true, message: '请选择部门', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'blur' }
        ],
        faceimg: [
          { required: true, message: '请选择照片上传或者拍照上传', trigger: 'blur' }
        ]
      },
      department: [],
      roles: [],
      loadingData: false,
      imgSrc: "",
      visible: false,//弹窗
      loading: false,//上传按钮加载
      open: false,//控制摄像头开关
      thisVideo: null,
      thisContext: null,
      thisCancas: null,
      videoWidth: 500,
      videoHeight: 400,
      employeename: '',
      email: '',
      phone: '',
    }
  },
  //进入页面刷新数据
  created() {
    //请求分页查询数据
    this.load()
  },
  mounted() {

  },
   beforeMount() {
    if (window.history && window.history.pushState) {
      history.pushState(null, null, location.href)
      window.addEventListener('popstate', this.goBack)
    }
  },
  beforeUnmount() {
    window.removeEventListener('popstate', this.goBack)
  },

  methods: {
    _flag2name(flag) {
      return flag2name(flag, this.roles)
    },
    // 自定返回事件
    goBack() {
      this.$router.go(0)
    },
    //条件查询
    searchOptions() {
      this.tableData = this.department.filter((item) => {
        // 检查员工名是否包含输入
        const employeenameMatch = this.employeename === "" || (item.employeename && item.employeename.indexOf(this.employeename) > -1);

        // 检查邮箱是否包含输入的邮箱
        const emailMatch = this.email === "" || (item.email && item.email.indexOf(this.email) > -1);

        // 检查电话号码是否包含输入的电话号码
        const phoneMatch = this.phone === "" || (item.phone && item.phone.indexOf(this.phone) > -1);

        // 如果员工名、邮箱和电话号码都匹配，则返回 true
        return employeenameMatch && emailMatch && phoneMatch;
      })

    },
    //分页查询
    load() {
      this.loadingData = true
      this.request.get("/employee/page", {
        params: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          employeename: this.employeename,
          phone: this.phone,
          email: this.email,
          departmentname: this.departmentname
        }
      }).then(res => {
        this.tableData = res.data.records
        this.total = res.data.total
        this.loadingData = false
      });
      this.request.get('/department/all').then(res => {
        this.department = res.data
      })
      this.request.get('/role').then(res => {        
        this.roles = res.data
        console.log(this.roles)
      })
    },
    //修改用户状态
    updateState(id, status) {
      this.request.get(`/employee/status?id=${id}&status=${status}`).then(res => {
        if (res.code == 200) {
          ElMessage({
            showClose: true,
            type: "success",
            message: "修改状态成功",
          })
          this.load()
        } else {
          ElMessage({
            showClose: true,
            type: "error",
            message: res.msg,
          })
          this.load()
        }
      })
    },
    //页数
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.load()
    },
    //页码
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.load()
    },
    //搜索重置按钮
    reset() {
      this.employeename = ""
      this.departmentname = ""
      this.phone = ""
      this.email = ""
      this.load()
    },
    //新增
    save() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.request.post("/employee/save", this.form).then(res => {
            if (res.code == 200) {
              ElMessage({
                showClose: true,
                type: "success",
                message: "保存成功",
              })
              this.load()
              this.addFormVisible = false
            } else {
              ElMessage({
                showClose: true,
                type: "error",
                message: "保存失败",
              })
            }
          })
        }
      })
    },
    //编辑
    edit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.request.post("/employee/edit", this.form).then(res => {
            if (res.code == 200) {
              ElMessage({
                showClose: true,
                type: "success",
                message: "保存成功",
              })
              this.load()
              this.editFormVisible = false
            } else {
              ElMessage({
                showClose: true,
                type: "error",
                message: res.msg,
              })
            }
          })
        }
      })
    },
    //打开新增弹窗
    handleAdd() {
      this.addFormVisible = true
      this.form = {status: "0", role: "ROLE_NORMAL"}
    },
    //编辑
    handleEdit(row, id) {
      // console.log(row, id)
      this.form.id = id
      this.form = JSON.parse(JSON.stringify(row))  //深拷贝
      this.editFormVisible = true
    },
    //根据ID删除
    handleDelete(employeeid) {
      ElMessageBox.confirm(
        this.$t('common.sureDel'), 
        this.$t('common.warning'),
        {
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          type: 'warning',

        }).then(() => {
          this.request.delete(`/employee/delete/${employeeid}`).then(res => {
            if (res.code == 200) {
              ElMessage({
                showClose: true,
                type: "success",
                message: this.$t('common.msgOk'),
              })
              this.load()
            } else {
              ElMessage({
                showClose: true,
                type: "error",
                message: this.$t('common.msgErr'),
              })
              this.load()
            }
          })
        })

    },
    //批量删除 val:数组
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.delBtlStatus = val.length === 0
    },
    //批量删除
    deleteBatch(employeeid) {
      let ids = []
      if (employeeid) {
        ids.push(employeeid)
      } else {
        this.multipleSelection.forEach(row => {
          ids.push(row.employeeid)
        })
      }
      ElMessageBox.confirm(
        this.$t('common.sureDel'), 
        this.$t('common.warning'),
        {
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          type: 'warning',

        }).then(async () => {
          await this.deleteArray(ids[0])
        })

    },

    //批量删除
    deleteArray(array) {
      // 创建一个包含所有请求的 Promise 数组
      const promises = array.map(element => {
        return this.request.delete(`/employee/delete/${element.employeeid}`);
      });
      // 使用 Promise.all() 等待所有请求完成
      Promise.all(promises).then(responses => {
        // 检查所有响应的状态码
        const allSuccess = responses.every(res => res.code === 200);
        if (allSuccess) {
          ElMessage({
            showClose: true,
            type: "success",
            message: this.$t('common.msgOk'),
          });
          this.load();
          this.multipleSelection = [];
          this.delBtlStatus = true;
        } else {
          ElMessage({
            showClose: true,
            type: "error",
            message: this.$t('common.msgErr'),
          });
        }
      });
    },
    //导出
    exp() {
      window.open(this.$store.state.base_url + "employee/export")
    },
    //设置上传规则
    beforeUpload(file) {
      if (file) {
        const suffix = file.name.split('.')[1]
        const size = file.size / 1024 / 1024 < 2
        if (['png', 'jpeg', 'jpg'].indexOf(suffix) < 0) {
          ElMessage.error('上传图片只支持 png、jpeg、jpg 格式！')
          this.$refs.upload.clearFiles()
          return false
        }
        if (!size) {
          ElMessage.error('上传文件大小不能超过 2MB!')
          return false
        }
        return file
      }
    },
    //上传成功回调
    handleAvatarSuccess(response, file, fileList) {
      //this.form.faceimg = `http://47.92.157.229:8888/download?name=${response.data}`
    },
    onTake() {
      this.visible = true;
      this.getCompetence();
    },
    onCancel() {
      this.visible = false;
      this.resetCanvas();
      this.stopNavigator();
    },
    //图片上传
    uploadImg(fileObj) {
      let formData = new FormData();
      formData.append("file", fileObj.file)
      formData.append("employeeid", this.employeeid)
      this.request.post("/registered", formData).then(res => {
        if (res.code === 200) {
          this.form.faceimg = res.data
          ElMessage({
            showClose: true,
            type: 'success',
            message: '人脸照片上传成功，请点击确定',
          })
          this.visible = false
          this.load()
        } else {
          ElMessage({
            showClose: true,
            type: 'error',
            message: '人脸照片上传识别，请重新上传',
          })
        }
      })
    },
    //base64转成文件后上传
    onUpload() {
      if (this.imgSrc) {
        console.log('拍照', this.imgSrc)
        let formData = new FormData()
        formData.append("file", this.base64ToFile(this.imgSrc, "png"));
        formData.append("flag", "videoImg"); // 额外参数
        console.log('拍照', formData)
        this.loading = true
        this.request.post("/registered", formData, { params: { employeeid: this.employeeid } }).then(res => {
          if (res.code === 200) {
            this.form.faceimg = res.data
            ElMessage({
              showClose: true,
              type: 'success',
              message: '人脸照片上传成功，请点击确定',
            })
            this.visible = false
          } else {
            ElMessage({
              showClose: true,
              type: 'error',
              message: '人脸照片上传识别，请重新上传',
            })
          }
          this.loading = false
        })
      }
      else {
        ElMessage({
          showClose: true,
          message: '请点击拍照',
          type: 'warning',
        });
      }
    },
    // 调用摄像头权限
    getCompetence() {
      //必须在model中render后才可获取到dom节点,直接获取无法获取到model中的dom节点
      this.$nextTick(() => {
        const _this = this;
        this.open = false;//切换成关闭摄像头
        this.thisCancas = document.getElementById('canvasCamera');
        this.thisContext = this.thisCancas.getContext('2d');
        this.thisVideo = document.getElementById('videoCamera');
        // 旧版本浏览器可能根本不支持mediaDevices，我们首先设置一个空对象
        if (navigator.mediaDevices === undefined) {
          navigator.mediaDevices = {}
        }
        // 一些浏览器实现了部分mediaDevices，我们不能只分配一个对象
        // 使用getUserMedia，因为它会覆盖现有的属性。
        // 这里，如果缺少getUserMedia属性，就添加它。
        if (navigator.mediaDevices.getUserMedia === undefined) {
          navigator.mediaDevices.getUserMedia = function (constraints) {
            // 首先获取现存的getUserMedia(如果存在)
            let getUserMedia = navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.getUserMedia;
            // 有些浏览器不支持，会返回错误信息
            // 保持接口一致
            if (!getUserMedia) {
              return Promise.reject(new Error('getUserMedia is not implemented in this browser'))
            }
            // 否则，使用Promise将调用包装到旧的navigator.getUserMedia
            return new Promise(function (resolve, reject) {
              getUserMedia.call(navigator, constraints, resolve, reject)
            })
          }
        }
        const constraints = {
          audio: false,
          video: { width: _this.videoWidth, height: _this.videoHeight, transform: 'scaleX(-1)' }
        };
        navigator.mediaDevices.getUserMedia(constraints).then(function (stream) {
          // 旧的浏览器可能没有srcObject
          if ('srcObject' in _this.thisVideo) {
            _this.thisVideo.srcObject = stream
          } else {
            // 避免在新的浏览器中使用它，因为它正在被弃用。
            _this.thisVideo.src = window.URL.createObjectURL(stream)
          }
          _this.thisVideo.onloadedmetadata = function (e) {
            _this.thisVideo.play()
          }
        }).catch(err => {
          ElMessage({
            showClose: true,
            message: '没有开启摄像头权限或浏览器版本不兼容.',
            type: 'warning'
          });
        });
      });
    },
    //绘制图片
    drawImage() {
      // 点击，canvas画图
      this.thisContext.drawImage(this.thisVideo, 0, 0, this.videoWidth, this.videoHeight);
      // 获取图片base64链接
      this.imgSrc = this.thisCancas.toDataURL('image/png');
    },
    // base64 转为 file
    base64ToFile(urlData, fileName) {
      let arr = urlData.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bytes = atob(arr[1]); // 解码base64
      let n = bytes.length;
      let ia = new Uint8Array(n);
      while (n--) {
        ia[n] = bytes.charCodeAt(n);
      }
      return new File([ia], fileName, { type: mime });
    },
    //清空画布
    clearCanvas(id) {
      let c = document.getElementById(id);
      let cxt = c.getContext("2d");
      cxt.clearRect(0, 0, c.width, c.height);
    },
    //重置画布
    resetCanvas() {
      this.imgSrc = "";
      this.clearCanvas('canvasCamera');
    },
    //关闭摄像头
    stopNavigator() {
      if (this.thisVideo) {
        this.thisVideo.srcObject.getTracks()[0].stop();
        this.open = true;//切换成打开摄像头
      }
    }

  }
}
</script>

<style>
.headerBg {
  background: #eee !important;
}

.avatar-uploader {
  margin-bottom: -10px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 130px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  display: block;
}

.avatar-uploader .el-icon-plus:before {
  content: "上传人脸照片" !important;
  font-size: 12px;
  color: #000;
}
</style>