<template>
  <div id="div">
    <div id="div" style="width: 180px">
      <!-- <el-menu mode="vertical">
        <el-menu-item index="1">首页</el-menu-item>
        <el-sub-menu index="2">
          <template #title>广场</template>
<el-menu-item index="2-1">音乐</el-menu-item>
<el-menu-item index="2-2">视频</el-menu-item>
<el-menu-item index="2-3">游戏</el-menu-item>
<el-sub-menu index="2-4">
  <template #title>体育</template>
  <el-menu-item index="2-4-1">篮球</el-menu-item>
  <el-menu-item index="2-4-2">足球</el-menu-item>
  <el-menu-item index="2-4-3">排球</el-menu-item>
</el-sub-menu>
</el-sub-menu>
<el-menu-item index="3" :disabled="true">个人中心</el-menu-item>
<el-menu-item index="4">设置</el-menu-item>
</el-menu> -->
      <!-- <el-menu>
        <div v-for="item in menus" :key="item.id">
          <div v-if="item.path">
            <el-menu-item :index="item.path">
              <component :is="item.icon" style="width: 18px; height: 18px; margin-right: 4px;" :title="item.name">
              </component>
              <template #title>{{ item.name }}</template>
            </el-menu-item>
          </div>
        </div>
      </el-menu> -->
      <el-menu style="min-height: 100vh;overflow-x: hidden" background-color="#3E2723" text-color="#FFF8E7"
        active-text-color="#DAA520" :collapse-transition="false" :collapse="isCollapse" :default-active="$route.path"
        router>
        <div style="height: 60px;line-height: 60px;text-align: center">
          <img src="../assets/logo.svg" alt="" style="width: 20px;position: relative;top: 5px;margin-right: 5px">
          <b style="color: #000" v-show="logoTextShow">{{ $t("common.description") }}</b>
        </div>
        <div v-for="item in menus" :key="item.id">
          <div v-if="item.path">
            <el-menu-item :index="item.path">
              <component :is="item.icon" style="width: 18px; height: 18px; margin-right: 4px;" :title="item.name">
              </component>
              <template #title>{{ item.name }}</template>
            </el-menu-item>
          </div>
          <div v-else>
            <el-sub-menu :index="item.id + ''">
              <template #title>
                <component :is="item.icon" style="width: 18px; height: 18px; margin-right: 4px;" />
                <span>{{ item.name }}</span>
              </template>
              <div v-for="subItem in item.children" :key="subItem.id">
                <el-menu-item :index="subItem.path">
                  <component :is="subItem.icon" style="width: 18px; height: 18px" />
                  <template #title>{{ subItem.name }}</template>
                </el-menu-item>
              </div>
            </el-sub-menu>
          </div>
        </div>
      </el-menu>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      menus: localStorage.getItem("menus")
        ? JSON.parse(localStorage.getItem("menus"))
        : [],
      opens: localStorage.getItem("menus")
        ? JSON.parse(localStorage.getItem("menus")).map((v) => v.id + "")
        : [],
    };
  },
  beforeCreate() {
    const employee = {
      employeeid: 72,
      username: "wxy",
      password: "1dd577758759c22a1c5d658b2900d1d6",
      employeename: "测试人员甲",
      role: "ROLE_TEST",
      lang: "zh",
    };
    localStorage.setItem("employee", JSON.stringify(employee));
    const menus = [
      {
        id: 1,
        name: "首页",
        path: "/home",
        icon: "HomeFilled",
        description: "首页",
        pid: null,
        pagePath: "Home",
        children: [],
        sortNum: 1,
        lang: "zhCn",
      },
      {
        id: 35,
        name: "驴肉品牌管理",
        path: "/band",
        icon: "Help",
        description: "产品品牌管理",
        pid: null,
        pagePath: "/Client/band",
        children: [],
        sortNum: 3,
        lang: "zhCn",
      },
      {
        id: 36,
        name: "驴肉订单管理",
        path: "/client",
        icon: "Fold",
        description: "客户端管理",
        pid: null,
        pagePath: "/Client/client",
        children: [],
        sortNum: 4,
        lang: "zhCn",
      },
      {
        id: 84,
        name: "驴肉销售统计",
        path: "/pad",
        icon: "Cellphone",
        description: "PAD设备管理",
        pid: null,
        pagePath: "/Upgrade/pad",
        children: [],
        sortNum: 4,
        lang: "zhCn",
      },
      {
        id: 37,
        name: "产品管理",
        path: "/caidan",
        icon: "Calendar",
        description: "菜单版本管理",
        pid: null,
        pagePath: "/Client/menu",
        children: [],
        sortNum: 5,
        lang: "zhCn",
      },
      {
        id: 80,
        name: "驴肉加工管理",
        path: "/ecu",
        icon: "RefreshRight",
        description: "驴肉加工管理",
        pid: null,
        pagePath: "/Client/ecu",
        children: [],
        sortNum: 5,
        lang: "zhCn",
      },
      {
        id: 38,
        name: "驴肉质检报告",
        path: "/report",
        icon: "EditPen",
        description: "诊断报告管理",
        pid: null,
        pagePath: "/Diagnostic/report",
        children: [],
        sortNum: 6,
        lang: "zhCn",
      },
      {
        id: 43,
        name: "驴肉加工流程",
        path: "/program",
        icon: "Menu",
        description: "诊断程序管理",
        pid: null,
        pagePath: "/Diagnostic/program",
        children: [],
        sortNum: 7,
        lang: "zhCn",
      },
      {
        id: 40,
        name: "驴肉配方版本"}]}}}
        path: "/apps",
        icon: "Refresh",
        description: "软件版本管理",
        pid: null,
        pagePath: "/App/apps",
        children: [],
        sortNum: 9,
        lang: "zhCn",
      },
      {
        id: 9,
        name: "业务管理员",
        path: null,
        icon: "Avatar",
        description: "业务管理员",
        pid: null,
        pagePath: null,
        children: [
          {
            id: 12,
            name: "员工管理",
            path: "/search",
            icon: null,
            description: null,
            pid: 9,
            pagePath: "Personnel management/Search",
            children: null,
            sortNum: null,
            lang: "zhCn",
          },
        ],
        sortNum: 13,
        lang: "zhCn",
      },
      {
        id: 14,
        name: "权限设置",
        path: null,
        icon: "Share",
        description: null,
        pid: null,
        pagePath: null,
        children: [
          {
            id: 15,
            name: "角色配置",
            path: "/role",
            icon: null,
            description: null,
            pid: 14,
            pagePath: "sys/Role",
            children: null,
            sortNum: null,
            lang: "zhCn",
          },
          {
            id: 16,
            name: "菜单配置",
            path: "/menu",
            icon: null,
            description: null,
            pid: 14,
            pagePath: "sys/Menu",
            children: null,
            sortNum: null,
            lang: "zhCn",
          },
        ],
        sortNum: 14,
        lang: "zhCn",
      },
    ];
    localStorage.setItem("menus", JSON.stringify(menus));
    const token =
      "eyJhbGciOiJIUzUxMiJ9.eyJhX2lkIjo3MiwiZXhwIjoxNzE2NzY2MDc2fQ.mm-_sVoa5dcGUhoFAaaHfUDkXL76L4h6qTXOwF3Pese1ZnNePbtz91WL9Pe1FUY_VYsAJqZEJVYWY3bikOLOxw";
    localStorage.setItem("token", JSON.stringify(token));
  },
  methods: {},
};
</script>

<style scoped></style>
