import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import CryptoJS from 'crypto-js';
import jwt from 'jsonwebtoken';

const app = express();
const PORT = process.env.PORT || 3000;

// JWT Secret Key - in production this should be in environment variables
const JWT_SECRET = 'your-jwt-secret-key';

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// Mock Database
const users = [
  {
    employeeid: 1,
    username: 'admin',
    password: '123456', // In real app, store hashed passwords only
    employeename: '管理员',
    role: 'ROLE_ADMIN',
    lang: 'zh'
  },
  {
    employeeid: 2,
    username: 'test',
    password: '123456', // In real app, store hashed passwords only
    employeename: '测试用户',
    role: 'ROLE_USER',
    lang: 'zh'
  }
];

// Mock menu data
const adminMenus = [
  {
    id: 1,
    name: "首页",
    path: "/home",
    icon: "HomeFilled",
    description: "首页",
    pid: null,
    pagePath: "dashboard/DashboardHome",
    children: [],
    sortNum: 1,
    lang: "zhCn"
  },
  {
    id: 30,
    name: "驴肉业务管理",
    path: null,
    icon: "Avatar",
    description: "业务总览",
    pid: null,
    pagePath: null,
    children: [
      {
        id: 31,
        name: "产品管理",
        path: "/vci",
        icon: null,
        description: "产品设备管理",
        pid: 30,
        pagePath: "/SysMgr/vci",
        children: null,
        sortNum: 1,
        lang: "zhCn"
      },
      {
        id: 32,
        name: "库存管理",
        path: "/repair",
        icon: null,
        description: "销售门店管理",
        pid: 30,
        pagePath: "/SysMgr/repair",
        children: null,
        sortNum: 2,
        lang: "zhCn"
      },
      {
        id: 33,
        name: "客户管理",
        path: "/user",
        icon: null,
        description: "用户账号",
        pid: 30,
        pagePath: "/SysMgr/user",
        children: null,
        sortNum: 3,
        lang: "zhCn"
      }
    ],
    sortNum: 2,
    lang: "zhCn"
  },
  {
    id: 35,
    name: "驴肉品牌管理",
    path: "/band",
    icon: "Help",
    description: "产品品牌管理",
    pid: null,
    pagePath: "/Client/band",
    children: [],
    sortNum: 3,
    lang: "zhCn"
  },
  {
    id: 80,
    name: "驴肉加工管理",
    path: "/ecu",
    icon: "RefreshRight",
    description: "驴肉加工管理",
    pid: null,
    pagePath: "/Client/ecu",
    children: [],
    sortNum: 5,
    lang: "zhCn"
  },
  {
    id: 41,
    name: "产品资料管理",
    path: "/ziliao",
    icon: "Document",
    description: "维修资料管理",
    pid: null,
    pagePath: "/Ziliao/ziliao",
    children: [],
    sortNum: 10,
    lang: "zhCn"
  },
  {
    id: 47,
    name: "产品手册管理",
    path: "",
    icon: "Film",
    description: "产品手册管理",
    pid: null,
    pagePath: "",
    children: [
      {
        id: 48,
        name: "产品宣传视频",
        path: "/guide",
        icon: null,
        description: "产品宣传视频管理",
        pid: 47,
        pagePath: "/Ziliao/guide",
        children: null,
        sortNum: 1,
        lang: "zhCn"
      },
      {
        id: 49,
        name: "产品手册",
        path: "/manual",
        icon: null,
        description: "产品手册",
        pid: 47,
        pagePath: "/Ziliao/manual",
        children: null,
        sortNum: 2,
        lang: "zhCn"
      }
    ],
    sortNum: 11,
    lang: "zhCn"
  },
  {
    id: 42,
    name: "产品质检管理",
    path: "",
    icon: "ArrowUp",
    description: "产品质检管理",
    pid: null,
    pagePath: "",
    children: [
      {
        id: 44,
        name: "包装规格",
        path: "/pcb",
        icon: null,
        description: "包装规格",
        pid: 42,
        pagePath: "/Upgrade/pcb",
        children: null,
        sortNum: 1,
        lang: "zhCn"
      },
      {
        id: 45,
        name: "配方版本",
        path: "/sdk",
        icon: null,
        description: "配方版本",
        pid: 42,
        pagePath: "/Upgrade/sdk",
        children: null,
        sortNum: 2,
        lang: "zhCn"
      },
      {
        id: 46,
        name: "软件版本",
        path: "/appv",
        icon: null,
        description: "软件版本",
        pid: 42,
        pagePath: "/Upgrade/appv",
        children: null,
        sortNum: 3,
        lang: "zhCn"
      }
    ],
    sortNum: 12,
    lang: "zhCn"
  },
  {
    id: 9,
    name: "业务管理员",
    path: null,
    icon: "Avatar",
    description: "业务管理员",
    pid: null,
    pagePath: null,
    children: [
      {
        id: 12,
        name: "员工管理",
        path: "/search",
        icon: null,
        description: null,
        pid: 9,
        pagePath: "Personnel management/Search",
        children: null,
        sortNum: null,
        lang: "zhCn"
      }
    ],
    sortNum: 13,
    lang: "zhCn"
  },
  {
    id: 14,
    name: "权限设置",
    path: null,
    icon: "Share",
    description: null,
    pid: null,
    pagePath: null,
    children: [
      {
        id: 15,
        name: "角色配置",
        path: "/role",
        icon: null,
        description: null,
        pid: 14,
        pagePath: "sys/Role",
        children: null,
        sortNum: null,
        lang: "zhCn"
      },
      {
        id: 16,
        name: "菜单配置",
        path: "/menu",
        icon: null,
        description: null,
        pid: 14,
        pagePath: "sys/Menu",
        children: null,
        sortNum: null,
        lang: "zhCn"
      }
    ],
    sortNum: 14,
    lang: "zhCn"
  }
];

// Helper function to hash password
const hashPassword = (password) => {
  return CryptoJS.MD5(password).toString();
};

// Helper function to generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ a_id: userId, exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 30) }, JWT_SECRET, { algorithm: 'HS512' });
};

// Login route
app.post('/web/employee/login', (req, res) => {
  const { username, password, lang } = req.body;
  
  console.log('Login attempt:', { username, password, lang });
  
  // Find user
  const user = users.find(u => u.username === username);
  
  if (!user) {
    return res.json({
      code: 401,
      msg: '用户名或密码错误',
      data: null
    });
  }
  
  // In a real app, compare hashed passwords
  if (user.password !== password) {
    return res.json({
      code: 401,
      msg: '用户名或密码错误',
      data: null
    });
  }
  
  // Generate token
  const token = generateToken(user.employeeid);
  
  // Create response data
  const responseData = {
    employeeid: user.employeeid,
    username: user.username,
    password: hashPassword(user.password), // In real app, never send password back to client
    employeename: user.employeename,
    token: token,
    role: user.role,
    lang: lang || user.lang,
    menus: adminMenus
  };
  
  // Return success response
  return res.json({
    code: 200,
    msg: '操作成功',
    data: responseData
  });
});

// Get user by username
app.get('/web/employee/username/:username', (req, res) => {
  const { username } = req.params;
  
  const user = users.find(u => u.username === username);
  
  if (!user) {
    return res.json({
      code: 404,
      msg: '用户不存在',
      data: null
    });
  }
  
  // Don't send password in response
  const { password, ...userData } = user;
  
  return res.json({
    code: 200,
    msg: '操作成功',
    data: userData
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
