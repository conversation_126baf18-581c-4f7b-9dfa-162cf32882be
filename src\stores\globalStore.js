import { defineStore } from "pinia";
import router from "@/router";

export const useGlobalStore = defineStore("GlobalStore", {
  // state
  state: () => {
    return {
      currentPathName: "",
      base_url: "/web/",
    };
  },
  // actions
  actions: {
    setPath() {
      this.currentPathName = localStorage.getItem("currentPathName");
    },
    logout() {
      localStorage.removeItem("employee");
      localStorage.removeItem("menus");
      localStorage.removeItem("token");
      router.push("/login");
      //重置路由
      // resetRouter();
    },
  },
  // getters
});
