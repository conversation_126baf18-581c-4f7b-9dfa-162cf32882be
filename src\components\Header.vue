<template>
    <div class="h-16 flex items-center justify-between bg-dark text-neutral px-4 shadow-elevation-1">
      <div class="flex-1 flex items-center space-x-4">
        <div class="cursor-pointer p-2 rounded-full hover:bg-primary/20 transition-colors" @click="collapse">
          <el-icon size="20px" v-if="collapseBtnClass === 'el-icon-s-fold'">
            <ArrowLeft />
          </el-icon>
          <el-icon size="20px" v-else>
            <ArrowRight />
          </el-icon>
        </div>
        <span class="font-mono text-sm text-neutral/80">{{ $t('common.time') }}{{ nowDate }}</span>
      </div>
      <el-switch v-model="langValue" class="mx-4" active-text="英文" inactive-text="中文" @change="switchLang" />
      <div id="full-screen" class="mr-4 cursor-pointer flex items-center p-2 rounded-full hover:bg-primary/20 transition-colors">
        <el-icon :size="20" style="margin-right: 3px;" @click="fullScreen()">
          <FullScreen />
        </el-icon>
        <span>{{$t("common.full")}}</span>
      </div>
      <el-dropdown style="width: 100px; cursor: pointer" trigger="click">
        <span class="el-dropdown-link flex items-center p-2 rounded-full hover:bg-primary/20 transition-colors">
          {{ employee.username }}
          <el-icon :size="20" style="margin-left: 8px">
            <Setting />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu style="padding-left: 8px; width: 100px; text-align: center">
            <el-dropdown-item style="font-size: 14px; padding: 5px 0">
              <span @click="person">{{ $t("common.header.person") }}</span>
            </el-dropdown-item>
            <el-dropdown-item style="font-size: 14px; padding: 5px 0">
              <span @click="updatePass">{{ $t("common.header.pass") }}</span>
            </el-dropdown-item>
            <el-dropdown-item style="font-size: 14px; padding: 5px 0">
              <span @click="logout">{{ $t("common.header.logout") }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </template>
  
  <script>
  import screenfull from 'screenfull'
  import dayjs from 'dayjs' //时间格式转换插件
  import { useGlobalStore } from "@/stores/globalStore";
  export default {
    name: "Header",
    props: {
      collapseBtnClass: String,
      employee: Object
    },
    data() {
      return {
        nowDate: '',
        langValue: false,
      }
    },
    computed: {
      currentPathName() {
        return this.$store.state.currentPathName;//需要监听的数据
      }
    },
    created() {
      setInterval(() => {
        this.nowDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
      }, 10)
      let lang = localStorage.getItem('lang')
      if (lang == null) {
        this.langValue = false
        return
      }
      if (lang === 'en') {
        this.langValue = true
      }
    },
    methods: {
      collapse() {
        this.$emit("asideCollapse")
      },
      logout() {
        ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(() => {
          useGlobalStore().logout()
          // this.$store.commit("logout")
        }).catch(() => {
          ElMessage({
            type: 'info',
            message: '退出取消',
          })
        })
      },
      updatePass() {
        this.$router.push('/updatePwd')
      },
      person() {
        this.$router.push('/person')
      },
      fullScreen() {
        if (!screenfull.isEnabled) {
          ElMessage({
            showClose: true,
            message: 'you browser can not work',
            type: 'warning'
          })
          return false
        }
        screenfull.toggle()
      },
      switchLang() {
        console.log(this.langValue)
        if (this.langValue) {
          localStorage.setItem("lang", "en")
        }
        else localStorage.setItem("lang", "zh")
        // 更新菜单    
        this.request
          .get("/menu/getMenus", {
            params: {
              roleFlag: JSON.parse(localStorage.getItem("employee")).role,
              lang: localStorage.getItem("lang")
            },
          })
          .then((res) => {
            if (res.code !== 200) {
              ElMessage({
                showClose: true,
                type: "error",
                message: res.msg,
              })
            } else {
              console.log(res)
              localStorage.setItem("menus", JSON.stringify(res.data))
              location.reload();
            }
          }).catch(e => {
            ElMessage({
              showClose: true,
              type: "error",
              message: e,
            })
          });
      }
    }
  }
  </script>
  
  <style scoped></style>