<template>
  <div class="bigBox">
    <div style="width: 500px;height: 350px;margin: 150px auto" class="box">
      <div style="text-align: center;font-size: 30px;padding: 30px">{{$t('login.title')}}</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item :label="$t('login.account')" prop="username">
          <el-input prefix-icon="el-icon-user" v-model="ruleForm.username" :placeholder="$t('login.hitUser')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('login.pass')" prop="password">
          <el-input prefix-icon="el-icon-lock" type="password" show-password v-model="ruleForm.password"
            :placeholder="$t('login.hitPass')"></el-input>
        </el-form-item>
        <!-- <div class="register"><router-link to="/register">免费注册</router-link></div> -->
        <el-form-item>
          <div class="jz">
            <el-button type="primary" :loading="loading" @click.native.prevent="submitForm('ruleForm')" class="btnLongin">
            <span v-if="!loading">{{$t('login.btnName')}}</span>
            <span v-else>{{$t('login.btnHit')}}</span>
          </el-button>&nbsp;&nbsp;
          <el-button @click="resetForm('ruleForm')" class="btnReset">{{ $t('common.reset') }}</el-button>
          </div>
          
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { setRoutes } from "@/router";

export default {
  name: "Login",
  data() {
    return {
      ruleForm: {
        username: 'admin',
        password: '',
        lang: localStorage.getItem('lang') === null || localStorage.getItem('lang') === '' || localStorage.getItem('lang') === undefined ? 'zh' : localStorage.getItem('lang')
      },
      loading: false,
      rules: {
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' },
          { min: 3, max: 15, message: '长度在 3 到 15 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'change' },
          { min: 6, max: 15, message: '长度在 6 到 15 个字符', trigger: 'blur' }
        ],
      }
    };
  },
  mounted() {
  },

   beforeMount() {
    if (window.history && window.history.pushState) {
      history.pushState(null, null, location.href)
      window.addEventListener('popstate', this.goBack)
    }
  },
  beforeUnmount() {
    window.removeEventListener('popstate', this.goBack)
  },

  methods: {
    // 自定返回事件
    goBack() {
      this.$router.go(0)
    },
    submitForm(ruleForm) {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.request.post("/web/employee/login", this.ruleForm).then(res => {  //获取动态路由接口
            if (res.code === 200) {
              localStorage.setItem("employee", JSON.stringify(res.data))  //把用户信息存在浏览器
              localStorage.setItem("menus", JSON.stringify(res.data.menus))
              localStorage.setItem("token", JSON.stringify(res.data.token))  //把token存在浏览器
              ElNotification({
                title: "登录成功",
                message: `欢迎回来~ 亲爱的${res.data.employeename}`,
                type: "success"
              });
              //设置当前用户的路由
              setRoutes()
              this.$router.push("/home")
            } else {
              ElMessage({
                showClose: true,
                message: res.msg,
                type: 'error',
                duration: 1000
              })
            }
            this.loading = false
          }).catch(e => {
            this.loadingData = false
            ElMessage({
              showClose: true,
              type: "error",
              message: e,
            })
          });
        }
      })
    },
    resetForm(ruleForm) {
      this.$refs['ruleForm'].resetFields();
    },
  }
}
</script>

<style scoped>
.box {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.demo-ruleForm {
  width: 450px;
}

.btnLongin {
  width: 100px;
  height: 35px;
  margin-top: 20px;
}

.btnFace {
  width: 100px;
  height: 35px;
  margin-top: 20px;
}

.btnReset {
  width: 100px;
  height: 35px;
  margin-top: 20px;
}

.register {
  font-size: 10px;
  margin-left: 400px;
}

a {
  text-decoration: none;
  color: #cccccc;
}

.el-input__inner {
  border-radius: 25px;
}
.jz {
  display: flex;
  justify-content: center;
  width: 100%;
}
</style>