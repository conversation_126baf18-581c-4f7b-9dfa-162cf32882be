<template>
  <div class="product-management-container p-6 bg-white rounded-lg shadow-md">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-gray-800">产品管理</h2>
      <el-button type="primary" @click="handleAddProduct">
        <Plus class="mr-2" /> 添加产品
      </el-button>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-container mb-6 p-4 bg-gray-50 rounded-lg">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入产品名称"
            prefix-icon="Search"
          />
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.category" placeholder="请选择产品分类">
            <el-option label="全部分类" value="" />
            <el-option label="驴肉制品" value="1" />
            <el-option label="生鲜驴肉" value="2" />
            <el-option label="驴肉礼盒" value="3" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="全部状态" value="" />
            <el-option label="在售" value="1" />
            <el-option label="下架" value="0" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch" class="ml-2">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 产品列表表格 -->
    <el-table
      :data="productList"
      border
      stripe
      style="width: 100%"
      :header-cell-style="{ 'background-color': '#f5f7fa', 'font-weight': 'bold' }"
    >
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column prop="name" label="产品名称" min-width="180" />
      <el-table-column prop="category" label="产品分类" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.category === '1'">驴肉制品</el-tag>
          <el-tag v-else-if="scope.row.category === '2'" type="success">生鲜驴肉</el-tag>
          <el-tag v-else-if="scope.row.category === '3'" type="info">驴肉礼盒</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="售价(元)" width="100" />
      <el-table-column prop="stock" label="库存数量" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope.row)" class="ml-2">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页控件 -->
    <div class="pagination-container mt-6 flex justify-end">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑产品对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="产品分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择产品分类">
            <el-option label="驴肉制品" value="1" />
            <el-option label="生鲜驴肉" value="2" />
            <el-option label="驴肉礼盒" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品价格" prop="price">
          <el-input v-model.number="form.price" placeholder="请输入产品价格" />
        </el-form-item>
        <el-form-item label="库存数量" prop="stock">
          <el-input v-model.number="form.stock" placeholder="请输入库存数量" />
        </el-form-item>
        <el-form-item label="产品描述">
          <el-input v-model="form.description" type="textarea" rows="4" placeholder="请输入产品描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

export default {
  name: 'ProductManagement',
  setup() {
    // 搜索表单数据
    const searchForm = reactive({
      name: '',
      category: '',
      status: ''
    });

    // 产品列表数据
    const productList = ref([
      {
        id: 1,
        name: '精品驴肉脯',
        category: '1',
        price: 88.00,
        stock: 150,
        status: '1',
        createTime: '2023-01-15 10:30:00'
      },
      {
        id: 2,
        name: '新鲜驴腿肉',
        category: '2',
        price: 128.00,
        stock: 80,
        status: '1',
        createTime: '2023-02-20 14:15:00'
      },
      {
        id: 3,
        name: '豪华驴肉礼盒',
        category: '3',
        price: 398.00,
        stock: 30,
        status: '1',
        createTime: '2023-03-10 09:45:00'
      },
      {
        id: 4,
        name: '五香驴肉罐头',
        category: '1',
        price: 45.00,
        stock: 200,
        status: '0',
        createTime: '2023-01-05 16:20:00'
      }
    ]);

    // 分页数据
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 4
    });

    // 对话框状态
    const dialogVisible = ref(false);
    const dialogTitle = ref('添加产品');
    const formRef = ref(null);
    const form = reactive({
      id: '',
      name: '',
      category: '',
      price: '',
      stock: '',
      description: ''
    });

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入产品名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      category: [
        { required: true, message: '请选择产品分类', trigger: 'change' }
      ],
      price: [
        { required: true, message: '请输入产品价格', trigger: 'blur' },
        { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
      ],
      stock: [
        { required: true, message: '请输入库存数量', trigger: 'blur' },
        { type: 'integer', min: 0, message: '库存必须为非负整数', trigger: 'blur' }
      ]
    };

    // 状态切换事件
    const handleStatusChange = (row) => {
      // 实际项目中这里会调用API更新状态
      ElMessage.success(`产品${row.name}状态已更新为${row.status === '1' ? '在售' : '下架'}`);
    };

    // 分页大小改变事件
    const handleSizeChange = (val) => {
      pagination.pageSize = val;
    };

    // 当前页码改变事件
    const handleCurrentChange = (val) => {
      pagination.currentPage = val;
    };

    // 保存产品按钮点击事件
    const handleSave = () => {
      formRef.value.validate((valid) => {
        if (valid) {
          if (form.id) {
            // 编辑产品
            const index = productList.value.findIndex(item => item.id === form.id);
            if (index !== -1) {
              productList.value.splice(index, 1, {
                ...form,
                createTime: productList.value[index].createTime
              });
              ElMessage.success('产品编辑成功');
            }
          } else {
            // 添加产品
            const newProduct = {
              ...form,
              id: Date.now(),
              createTime: new Date().toLocaleString()
            };
            productList.value.unshift(newProduct);
            pagination.total++;
            ElMessage.success('产品添加成功');
          }
          dialogVisible.value = false;
        }
      });
    };

    return {
      searchForm,
      productList,
      pagination,
      dialogVisible,
      dialogTitle,
      form,
      rules,
      formRef,
      handleSearch,
      resetSearch,
      handleAddProduct,
      handleEdit,
      handleDelete,
      handleStatusChange,
      handleSizeChange,
      handleCurrentChange,
      handleSave,
      Plus
    };
  }
};
</script>

<style scoped>
.product-management-container {
  min-height: calc(100vh - 120px);
}
.filter-container {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
.pagination-container {
  margin-top: 16px;
}
</style>

    // 生命周期钩子 - 页面加载时执行
    onMounted(() => {
      // 实际项目中这里会调用API获取产品列表数据
      console.log('产品管理页面加载完成');
    });

    // 搜索按钮点击事件
    const handleSearch = () => {
      // 实际项目中这里会调用API进行搜索
      ElMessage.success('搜索功能已触发');
    };

    // 重置搜索条件
    const resetSearch = () => {
      searchForm.name = '';
      searchForm.category = '';
      searchForm.status = '';
    };

    // 添加产品按钮点击事件
    const handleAddProduct = () => {
      dialogTitle.value = '添加产品';
      form.id = '';
      form.name = '';
      form.category = '';
      form.price = '';
      form.stock = '';
      form.description = '';
      dialogVisible.value = true;
    };

    // 编辑产品按钮点击事件
    const handleEdit = (row) => {
      dialogTitle.value = '编辑产品';
      // 复制行数据到表单
      Object.assign(form, { ...row });
      dialogVisible.value = true;
    };

    // 删除产品按钮点击事件
    const handleDelete = (row) => {
      ElMessageBox.confirm(`确定要删除产品${row.name}吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实际项目中这里会调用API删除产品
        const index = productList.value.findIndex(item => item.id === row.id);
        if (index !== -1) {
          productList.value.splice(index, 1);
          pagination.total--;
          ElMessage.success('产品删除成功');
        }
      }).catch(() => {
        ElMessage.info('已取消删除');
      });