<template>
  <div>
    <!-- 面包屑导航区 -->
    <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-bottom: 20px">
      <el-breadcrumb-item :to="{ path: '/home' }">{{$t('common.home')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('common.sys.nav1')}}</el-breadcrumb-item>
      <el-breadcrumb-item>{{$t('common.sys.nav2')}}</el-breadcrumb-item>
    </el-breadcrumb>

    <div style="padding: 10px 0">
      <el-input style="width:250px" suffix-icon="el-icon-search" :placeholder="$t('common.sys.sysName')" v-model="name" clearable></el-input>
      <el-button style="margin-left: 3px" type="primary" @click="load">{{$t('common.search')}}</el-button>
      <el-button style="margin-left: 3px" type="warning" @click="reset">{{$t('common.reset')}}</el-button>
    </div>
    <div style="margin: 5px 0">
      <el-button type="primary" @click="handleAdd(null)">{{$t('common.add')}}<i class="el-icon-circle-plus-outline"></i></el-button>
      <el-button type="danger" slot="reference" :disabled="delBtlStatus" @click="deleteBatch(null)">{{$t('common.sys.delete')}}<i
          class="el-icon-delete"></i></el-button>
    </div>

    <el-table :data="tableData" v-loading="loading" border stripe :header-cell-class-name="headerBg" row-key="id"
      default-expand-all @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55 " />
      <el-table-column prop="id" label="ID" sortable>
      </el-table-column>
      <el-table-column prop="name" :label="$t('common.mc')">
      </el-table-column>
      <el-table-column prop="path" :label="$t('common.road')">
      </el-table-column>
      <el-table-column prop="pagePath" :label="$t('common.pageRoad')" width="220">
      </el-table-column>
      <el-table-column :label="$t('common.icon')" class-name="fontSize18" align="center" label-class-name="fontSize12">
        <template #default="scope">
          <i :class="scope.row.icon"></i>
        </template>
      </el-table-column>
      <el-table-column prop="description" :label="$t('common.desc')">
      </el-table-column>
      <el-table-column prop="sortNum" :label="$t('common.order')">
      </el-table-column>
      <el-table-column fixed="right" :label="$t('common.action')" width="300">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleAdd(scope.row.id)" v-if="!scope.row.path">{{$t('common.addMenu')}}<i
              class="el-icon-plus"></i></el-button>
          <el-button type="warning" size="small" @click="handleEdit(scope.row)">{{$t('common.edit')}}<i class="el-icon-edit"></i>
          </el-button>
          <el-button type="danger" size="small" slot="reference" @click="handleDelete(scope.row.id)">{{$t('common.del')}}<i
              class="el-icon-delete"></i></el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--弹窗-->
    <el-dialog :title="$t('common.menuInfo')" v-model="dialogFormVisible" width="30%">
      <el-form label-width="80px" :model="form" ref="form" :rules="formRlues">
        <el-form-item :label="$t('common.mc')" prop="name">
          <el-input v-model="form.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('common.road')" prop="path">
          <el-input v-model="form.path" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('common.pageRoad')" prop="pagePath">
          <el-input v-model="form.pagePath" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('common.icon')" prop="icon">
          <el-select clearable v-model="form.icon" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.value">
              <i :class="item.value" />{{ item.name }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('common.desc')" prop="description">
          <el-input v-model="form.description" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('common.order')" prop="sortNum">
          <el-input v-model="form.sortNum" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">{{$t('common.cancel')}}</el-button>
          <el-button type="primary" @click="save">{{$t('common.ok')}}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "Menu",
  data() {
    return {
      tableData: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      name: '',
      form: {},
      formRlues: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
      },
      multipleSelection: [],
      headerBg: 'headerBg',
      dialogFormVisible: false,
      options: [],
      delBtlStatus: true,
      loading: false
    }
  },
  //进入页面刷新数据
  created() {
    //请求分页查询数据
    this.load()
  },
   beforeMount() {
    if (window.history && window.history.pushState) {
      history.pushState(null, null, location.href)
      window.addEventListener('popstate', this.goBack)
    }
  },
  beforeUnmount() {
    window.removeEventListener('popstate', this.goBack)
  },

  methods: {
    // 自定返回事件
    goBack() {
      this.$router.go(0)
    },
    //分页查询
    load() {
      this.loading = true
      this.request.get("/menu", {
        params: {
          name: this.name,
          lang: localStorage.getItem('lang') === 'en' ? "en" : "zhCn"
        }
      }).then(res => {
        this.tableData = res.data
      });
      //请求图标的数据
      this.request.get("/dict/getAll").then(res => {
        this.options = res.data
        this.loading = false
      });
    },
    //搜索重置按钮
    reset() {
      this.name = ""
      this.load()
    },
    //新增
    save() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.request.post("/menu/save", this.form).then(res => {
            console.log(res)
            if (res.code === 200) {
              ElMessage({
                showClose: true,
                type: "success",
                message: "保存成功",
                duration: 1000
              })
              this.load()
              this.dialogFormVisible = false
            } else {
              ElMessage({
                showClose: true,
                type: "error",
                message: "保存失败",
                duration: 1000
              })
            }
          })
        }
      })
    },
    //打开新增弹窗
    handleAdd(pid) {
      this.dialogFormVisible = true
      this.form = {}
      if (pid) {
        this.form.pid = pid
      }
    },
    //编辑
    handleEdit(row) {
      this.form = JSON.parse(JSON.stringify(row))  //深拷贝
      this.dialogFormVisible = true
      //请求图标的数据
      this.request.get("/dict/getAll").then(res => {
        this.options = res.data
      });
    },
    //根据ID删除
    handleDelete(id) {
      console.log(id)
      ElMessageBox.confirm(this.$t('common.sureDel'),  this.$t('common.warning'), {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.request.delete("/menu/delete/" + id).then(res => {
          if (res.code === 200) {
            ElMessage({
              showClose: true,
              type: "success",
              message: this.$t('common.msgOk'),
              duration: 1000
            })
            this.load()
          } else {
            ElMessage({
              showClose: true,
              type: "error",
              message: this.$t('common.msgErr'),
              duration: 1000
            })
            this.load()
          }
        })
      })
    },
    //批量删除 val:数组
    handleSelectionChange(val) {
      this.multipleSelection = val
      this.delBtlStatus = val.length === 0
    },
    deleteBatch(id) {
      let ids = []
      if (id) {
        ids.push(id)
      } else {
        this.multipleSelection.forEach(row => {
          ids.push(row.id)
        })
      }
      ElMessageBox.confirm(this.$t('common.sureDel'),  this.$t('common.warning'), {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.request.post("/menu/delete/batch", ids).then(res => {
          if (res.code === 200) {
            ElMessage({
              showClose: true,
              type: "success",
              message: "批量删除成功",
              duration: 1000
            })
            this.load()
          } else {
            ElMessage({
              showClose: true,
              type: "error",
              message: "批量删除失败",
              duration: 1000
            })
          }
        })
      })
    },
  }
}
</script>

<style>
.headerBg {
  background: #eee !important;
}

.fontSize18 {
  font-size: 18px;
}

.fontSize12 {
  font-size: 12px;
}
</style>