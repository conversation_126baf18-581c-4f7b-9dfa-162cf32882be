// import './assets/base.css'
import './css/style.css'
import { createApp } from 'vue'
// 持久化状态
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-persistedstate-plugin'
import App from './App.vue'
import router from './router'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import i18n from '@/utils/lang'
import request from "@/util/request";

const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
// 使用pinia持久化
const pinia = createPinia()
const persist = createPersistedState()
pinia.use(persist)
app.use(pinia)

app.config.globalProperties.request = request;
app.use(createPinia())
app.use(router)
app.use(i18n)
app.mount('#app')
