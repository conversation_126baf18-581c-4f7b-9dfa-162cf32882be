<template>
    <el-row>
      <el-col :span="8">
        <!-- user卡片 -->
        <el-card class="shadow-elevation-1 hover:shadow-elevation-2 transition-shadow duration-300 rounded-lg overflow-hidden"/>
          <div class="user">
            <img src="@/assets/images/vp.png" alt="">
            <div class="userInfo">
              <p div class="name">Henry Ong</p>
              <p div class="access">VP：驴肉事业部</p>
            </div>
          </div>
          <div class="loginInfo">
            <p>本月还剩(天)：<span>15</span></p>
            <p>Office BASE：<span>上海</span></p>
          </div>
        </el-card>
        <!-- table卡片 -->
        <el-card style="margin-top: 20px;">
          <el-table :data="TableData" style="width: 100%">
            <!-- 这里的val,key对应的是对象里的 -->
            <!-- <el-table-column v-for="(item, index) in TableLabel" :key="index" :prop="item.key" :label="item.value">
            </el-table-column> -->
            <el-table-column prop="name" label="品牌" />
            <el-table-column prop="todayBuy" label="今日购买" />
            <el-table-column prop="monthBuy" label="本月购买" />
            <el-table-column prop="totalBuy" label="总计" />
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="16">
        <div class="num">
          <el-card v-for="item in CountData" :key="item.name" :body-style="{ display: 'flex', padding: 0 }">
            <i class="icon" :class="`el-icon-${item.icon}`" :style="{ backgroundColor: item.color }"></i>
            <div class="details">
              <p class="price">{{ priceFormate(item.value) }}</p>
              <p class="desc">{{ item.name }}</p>
            </div>
          </el-card>
        </div>
        <!-- echarts图表 -->
        <div style="margin-left:20px">
          <!-- 折线图 -->
          <el-card style="height:280px">
            <div ref="echarts1" style="height:280px"></div>
          </el-card>
          <div class="graph">
            <!-- 柱状图 -->
            <el-card style="height:320px">
              <div ref="echarts2" style="height:320px"></div>
            </el-card>
            <!-- 饼状图 -->
            <el-card style="height:320px">
              <div ref="echarts3" style="height:320px"></div>
            </el-card>
          </div>
        </div>
      </el-col>
    </el-row>
  </template>
  
  <script>
  import * as echarts from 'echarts'
  import tableData from '@/assets/demo/dashboard/tableData'
  import ml from './chart_template/multi_line_templ'
  import orderData from '@/assets/demo/dashboard/orderData'
  import pr from './chart_template/histogram_templ'
  import userData from '@/assets/demo/dashboard/userData'
  import pie from './chart_template/pie_templ'
  import videoData from '@/assets/demo/dashboard/videoData'
  
  export default {
    data() {
      return {
        TableData: tableData,
        TableLabel: [
          { name: "品牌" },
          { todayBuy: "今日购买" },
          { monthBuy: "本月购买" },
          { todayBuy: "总计" }
        ],
        CountData: [
          { name: "今日支付订单", value: "546,000" },
          { name: "今日未支付订单", value: "156,092" },
          { name: "今日订单", value: "702,092" },
          { name: "本月已收账款", value: "6,123,098" },
          { name: "本月未收账款", value: "1,345,602" },
          { name: "本月应收账款", value: "7,468,700" },
        ]
      }
    },
    methods: {
      priceFormate(price) {
        return "￥" + price
      }
    },
    mounted() {
      // 折线图
      // 基于准备好的dom，初始化echarts实例
      const echarts1 = echarts.init(this.$refs.echarts1)
      var echarts1Option = ml
      // 获取x轴:要求是一个对象
      const xAxis = Object.keys(orderData.data[0])
      const xAxisData = {
        data: xAxis
      }
      // 配置
      echarts1Option.legend = xAxisData
      echarts1Option.xAxis = xAxisData
      echarts1Option.yAxis = {}
      echarts1Option.series = []
      // 配置series
      xAxis.forEach(key => {
        echarts1Option.series.push({
          name: key,
          type: 'line',
          // key对应的orderData的所有值
          data: orderData.data.map(item => item[key])
        })
      })
  
      // 使用刚指定的配置项和数据显示图表。
      echarts1.setOption(echarts1Option);
      // 柱状图
      const echarts2 = echarts.init(this.$refs.echarts2)
      var echarts2Option = pr
      // 配置
      echarts2Option.xAxis.data = userData.map(item => item.date)
      echarts2Option.series = [
  {
    name: '新增用户',
    data: userData.map(item => item.new),
    type: 'bar',
    label: {
      show: true,
      position: 'top',
      textStyle: { color: '#333' }
    }
  },
  {
    name: '活跃用户',
    data: userData.map(item => item.active),
    type: 'bar',
    label: {
      show: true,
      position: 'top',
      textStyle: { color: '#333' }
    }
  }
]
echarts2Option.grid = {
  left: '5%',
  right: '5%',
  bottom: '10%',
  containLabel: true
};
  
      echarts2.setOption(echarts2Option);
      // 饼状图
      const echarts3 = echarts.init(this.$refs.echarts3)
      var echarts3Option = pie
      echarts3Option.series = {
  data: videoData,
  type: 'pie',
  center: ['50%', '50%'],
  label: {
    show: true,
    position: 'inside',
    textStyle: {
      color: '#fff',
      fontSize: 12
    }
  }
}
      echarts3.setOption(echarts3Option);
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .user {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;

    img {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      margin-right: 20px;
      border: 4px solid #fff;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .userInfo {
      .name {
        font-size: 24px;
        font-weight: 600;
        color: #3E2723;
        margin-bottom: 5px;
      }

      .access {
        color: #6B7280;
        font-size: 14px;
      }
    }
  }
  
  .loginInfo {
    p {
      line-height: 28px;
      font-size: 14px;
      color: #999999;
  
      span {
        color: #666666;
        margin-left: 60px;
      }
    }
  }
  
  .num {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;

    .el-card {
      border-radius: 8px;
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
      }

      .icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        border-radius: 8px 0 0 8px;
      }

      .details {
        flex: 1;
        padding: 16px;

        .price {
          font-size: 24px;
          font-weight: 700;
          color: #3E2723;
          margin-bottom: 4px;
        }

        .desc {
          color: #6B7280;
          font-size: 14px;
        }
      }
    }
  }    display: flex;
    // 要换行
    flex-wrap: wrap;
    // 从头到尾均匀排列
    justify-content: space-between;
    margin-left: 20px;
  
    .el-card {
      width: 32%;
      margin-bottom: 20px;
  
      .icon {
        width: 80px;
        height: 80px;
        line-height: 80px;
        text-align: center;
        font-size: 30px;
        color: #fff;
      }
  
      .details {
        // 竖着排且居中
        display: flex;
        flex-direction: column;
        justify-content: center;
  
        margin-left: 15px;
  
        .price {
          font-size: 30px;
          margin-bottom: 10px;
          line-height: 30px;
          height: 30px;
        }
  
        .desc {
          font-size: 14px;
          color: #999;
          text-align: center;
        }
      }
    }
  }
  
  .graph {
    display: flex;
    // 两个靠边
    justify-content: space-between;
    margin-top: 20px;
  
    .el-card {
      width: 49%;
    }
  }
  </style>
  