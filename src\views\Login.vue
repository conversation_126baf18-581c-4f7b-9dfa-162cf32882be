<template>
  <el-row class="login-page">
    <el-col :span="12" class="bg"></el-col>
    <el-col :span="6" :offset="3" class="form">
      <!-- 注册表单 -->
      <el-form ref="form" size="large" autocomplete="off" v-if="isRegister" :model="registerData" :rules="rules">
        <el-form-item>
          <h1>注册</h1>
        </el-form-item>
        <el-form-item prop="username">
          <el-input :prefix-icon="User" placeholder="请输入用户名" v-model="registerData.username"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input :prefix-icon="Lock" type="password" placeholder="请输入密码" v-model="registerData.password"></el-input>
        </el-form-item>
        <el-form-item prop="rePassword">
          <el-input :prefix-icon="Lock" type="password" placeholder="请输入再次密码"
            v-model="registerData.rePassword"></el-input>
        </el-form-item>
        <!-- 注册按钮 -->
        <el-form-item>
          <el-button class="button" type="primary" auto-insert-space @click="register">
            注册
          </el-button>
        </el-form-item>
        <el-form-item class="flex">
          <el-link type="info" :underline="false" @click="
            isRegister = false;
          clearRegisterData();
          ">
            ← 返回
          </el-link>
        </el-form-item>
      </el-form>
      <!-- 登录表单 -->
      <el-form ref="form" size="large" autocomplete="off" v-else :model="registerData" :rules="rules">
        <el-form-item>
          <h1>登录</h1>
        </el-form-item>
        <el-form-item prop="username">
          <el-input :prefix-icon="User" placeholder="请输入用户名" v-model="registerData.username"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input name="password" :prefix-icon="Lock" type="password" placeholder="请输入密码"
            v-model="registerData.password"></el-input>
        </el-form-item>
        <el-form-item class="flex">
          <div class="flex">
            <el-checkbox>记住我</el-checkbox>
            <el-link type="primary" :underline="false">忘记密码？</el-link>
          </div>
        </el-form-item>
        <!-- 登录按钮 -->
        <el-form-item>
          <el-button class="button" type="primary" :loading="loading" auto-insert-space @click="login">登录</el-button>
        </el-form-item>
        <el-form-item class="flex">
          <el-link type="info" :underline="false" @click="
            isRegister = true;
          clearRegisterData();
          ">
            注册 →
          </el-link>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script>
import { setRoutes } from "@/router";
export default {
  data() {
    return {
      registerData: {
        username: "wxylxm",
        password: "123456",
      },
      loading: false,
    };
  },
  beforeCreate() {
    const employee = {
      employeeid: 72,
      username: "wxy",
      password: "1dd577758759c22a1c5d658b2900d1d6",
      employeename: "测试人员甲",
      token:
        "eyJhbGciOiJIUzUxMiJ9.eyJhX2lkIjo3MiwiZXhwIjoxNzE2NzY2MDc2fQ.mm-_sVoa5dcGUhoFAaaHfUDkXL76L4h6qTXOwF3Pese1ZnNePbtz91WL9Pe1FUY_VYsAJqZEJVYWY3bikOLOxw",
      role: "ROLE_TEST",
      lang: "zh",      
    };
    localStorage.setItem("employee", JSON.stringify(employee));
    const menus = [
      {
        id: 1,
        name: "首页",
        path: "/home",
        icon: "HomeFilled",
        description: "首页",
        pid: null,
        pagePath: "Home",
        children: [],
        sortNum: 1,
        lang: "zhCn",
      }
    ];
    localStorage.setItem("menus", JSON.stringify(menus));
    const token =
      "eyJhbGciOiJIUzUxMiJ9.eyJhX2lkIjo3MiwiZXhwIjoxNzE2NzY2MDc2fQ.mm-_sVoa5dcGUhoFAaaHfUDkXL76L4h6qTXOwF3Pese1ZnNePbtz91WL9Pe1FUY_VYsAJqZEJVYWY3bikOLOxw";
    localStorage.setItem("token", JSON.stringify(token));
  },
  methods: {
    login() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.loading = true;
          this.request
            .post("/user/login", this.registerData)
            .then((res) => {
              //获取动态路由接口
              if (res.code === 200) {
                localStorage.setItem("login", JSON.stringify(res)); //把用户信息存在浏览器
                ElNotification({
                  title: "登录成功",
                  message: `欢迎回来~`,
                  type: "success",
                });
                //设置当前用户的路由
                setRoutes()
                setTimeout(() => {
                  this.$router.push("/");
                }, 1000);
              } else {
                ElMessage({
                  showClose: true,
                  message: res.msg,
                  type: "error",
                  duration: 1000,
                });
              }
              this.loading = false;
            })
            .catch((e) => {
              this.loadingData = false;
              console.log("error", e);
              ElMessage({
                showClose: true,
                type: "error",
                message: e,
              });
            });
        }
      });
    },
  },
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background-color: #FFF8E7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 420px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  background: linear-gradient(135deg, #8B4513 0%, #3E2723 100%);
  color: white;
  padding: 24px;
  text-align: center;
}

.login-title {
  font-family: 'Playfair Display', serif;
  font-size: 24px;
  margin-bottom: 8px;
}

.login-form {
  padding: 32px;
}

.form-item {
  margin-bottom: 20px;
}

.el-button--primary {
  background-color: #8B4513;
  border-color: #8B4513;
}

.el-button--primary:hover {
  background-color: #6d3810;
  border-color: #6d3810;
}

/* 样式 */
.login-page {
  height: 100vh;
  background-color: #fff;

  .bg {
    background: url("@/assets/logo2.png") no-repeat 60% center / 240px auto,
      url("@/assets/login_bg.jpg") no-repeat center / cover;
    border-radius: 0 20px 20px 0;
  }

  .form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    user-select: none;

    .title {
      margin: 0 auto;
    }

    .button {
      width: 100%;
    }

    .flex {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
