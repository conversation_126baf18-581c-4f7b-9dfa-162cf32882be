<template>
  <div>
    <el-config-provider :locale="locale">
      <el-container style="min-height: 100vh; ">
        <!--    侧边栏    -->
        <el-aside :width="sideWidth + 'px'" style="box-shadow: 2px 0 6px rgb(0 21 41 / 35%);">
          <Aside :isCollapse="isCollapse" :logoTextShow="logoTextShow" />
        </el-aside>

        <!--    顶部栏    -->
        <el-container>
          <el-header style="border-bottom: 1px solid #ccc">
            <Header :collapseBtnClass="collapseBtnClass" @asideCollapse="collapse" :employee="employee" />
          </el-header>
          <!--主体-->
          <el-main class="bg-neutral p-6">
          <img src="../assets/画板 1.png" class="w-full h-56 mt-0 mb-6 rounded-lg shadow-elevation-1 ml-0 mr-16 object-cover" alt="驴肉美食横幅"/>
            <router-view @refreshUser="getUser" />
          </el-main>
        </el-container>
      </el-container>
    </el-config-provider>
  </div>
</template>

<script>
import Aside from "@/components/Aside";
import Header from "@/components/Header";
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'
// import useUserInfoStore from '@/stores/userInfo.js'

export default {
  name: "Layout",
  data() {
    return {
      collapseBtnClass: 'el-icon-s-fold',
      isCollapse: false,
      sideWidth: 180,
      logoTextShow: true,
      employee: {},
      locale: zhCn,
    }
  },
  components: {
    Aside,
    Header
  },
  created() {
    this.locale = localStorage.getItem('lang') === 'en' ? en : zhCn
    this.getUser()
  },
  methods: {
    //侧边栏收缩
    collapse() {  //点击收缩按钮触发
      this.isCollapse = !this.isCollapse
      if (this.isCollapse) {   //收缩
        this.sideWidth = 60
        this.collapseBtnClass = 'el-icon-s-unfold'
        this.logoTextShow = false
      } else {   //展开
        this.sideWidth = 180
        this.collapseBtnClass = 'el-icon-s-fold'
        this.logoTextShow = true
      }
    },
    getUser() {
      let username = localStorage.getItem("employee") ? JSON.parse(localStorage.getItem("employee")).username : ""
      //从后台获取数据
      this.request.get("/web/employee/username/" + username).then(res => {
        // 增加无Token时的判断和跳转
        if (res.code === 401) {
          ElMessage({
            showClose: true,
            message: res.msg,
            type: "error",
            duration: 1000
          })
          this.$router.push("/login")
        } else {
          // 存储用户信息
          // const userInfoStore = useUserInfoStore()
          // userInfoStore.setInfo(res.data)
          this.employee = res.data
        }
      }).catch(e => {
        ElMessage({
          showClose: true,
          type: "error",
          message: e,
        })
        this.$router.push("/login")
      })
    }
  }
}
</script>

<style>
.el-aside {
  color: #333;
}
</style>