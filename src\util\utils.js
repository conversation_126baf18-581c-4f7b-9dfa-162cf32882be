export function downloadFile(url) {
  const link = document.createElement("a"); // 创建链接标签，即a标签，并将dom命名为link
  link.href = url; // 为dom为link的元素添加链接
  link.setAttribute("download", ""); // 为link设置下载属性
  document.body.appendChild(link); // 把创建并配置好的link dom添加到页面文档中
  link.click(); // 模拟dom的点击事件
  document.body.removeChild(link);
}

export function flag2name(flag, data) {
  let val = data.filter(item => item.flag === flag);
  return val.length === 0 ? flag + '' : val[0].name;
}

export const langs = [
  {
    lang: "en_US",
    label: "英文",
  },
  {
    lang: "zh_CN",
    label: "中文",
  },
  {
    lang: "de_DE",
    label: "德文",
  },
  {
    lang: "fr_FR",
    label: "法文",
  },
  {
    lang: "ja_<PERSON>",
    label: "日文",
  },
  {
    lang: "es_ES",
    label: "西班牙",
  },
  {
    lang: "tr_TR",
    label: "土耳其",
  },
  {
    lang: "pt_PT",
    label: "葡萄牙语",
  },
];

export function showInfo(row, lang) {
  if (row.language.length > 0) {
    for (var i = 0; i < row.language.length; i++) {
      if (row.language[i] === lang) {
        ElMessageBox.confirm(row.description[i], row.id, {
          type: "info",
        });
        return;
      }
    }
    ElMessageBox.confirm("没有设置描述", row.id, {
      type: "info",
    });
  } else {
    ElMessageBox.confirm("没有设置描述", row.id, {
      type: "info",
    });
  }
}
