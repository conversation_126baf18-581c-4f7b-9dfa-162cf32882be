import axios from "axios";
import router from "@/router/index.js";

const request = axios.create({
  baseURL: "/web",
  timeout: 120000,
});

// request 拦截器
// 可以自请求发送前对请求做一些处理
// 比如统一加token，对请求参数统一加密
request.interceptors.request.use(
  (config) => {
    // config.headers["Content-Type"] = "application/x-www-form-urlencoded"

    if(config.headers["Authorization"]===undefined || config.headers["Authorization"].length <=0) {
      let login = localStorage.getItem("login")
      if(login) {
        console.log("set.." + JSON.parse(login))
        config.headers["Authorization"] = JSON.parse(login).data
      }
    }    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// response 拦截器
// 可以在接口响应后统一处理结果
request.interceptors.response.use(
  (response) => {
    let res = response.data;

    // 如果token过期
    console.log("response code:" + res.code);
    if (res.code === 401) {
      return this.$router.push("/login");
    }

    // 如果是返回的文件
    if (response.config.responseType === "blob") {
      return res;
    }
    // 兼容服务端返回的字符串数据
    if (typeof res === "string") {
      res = res ? JSON.parse(res) : res;
    }
    //当权限验证不通过时的提示
    if (res.code === 401) {
      ElMessage({
        showClose: true,
        message: res.msg,
        type: "error",
      });
    }
    return res;
  },
  (error) => {
    console.log("error.response.status：" + error);
    let message = "";
    if (error && error.response) {
      switch (error.response.status) {
        case 302:
          message = "接口重定向了！";
          break;
        case 400:
          message = "参数不正确！";
          break;
        case 401:
          message = "您未登录，或者登录已经超时，请先登录！";
          // 清除本地信息
          localStorage.clear();
          // 跳转到登录页
          router.push("/");
          break;
        case 403:
          message = "您没有权限操作！";
          break;
        case 404:
          message = `请求地址出错: ${error.response.config.url}`;
          break;
        case 408:
          message = "请求超时！";
          break;
        case 409:
          message = "系统已存在相同数据！";
          break;
        case 500:
          message = "服务器内部错误！";
          break;
        case 501:
          message = "服务未实现！";
          break;
        case 502:
          message = "网关错误！";
          break;
        case 503:
          message = "服务不可用！";
          break;
        case 504:
          message = "服务暂时无法访问，请稍后再试！";
          break;
        case 505:
          message = "HTTP 版本不受支持！";
          break;
        default:
          message = "异常问题，请联系管理员！";
          break;
      }
    }
    return Promise.reject(message);
  }
);

export default request;
