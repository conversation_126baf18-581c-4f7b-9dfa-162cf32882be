export default {
    // 公共部分
    common: {
        welcome: "欢迎使用管理系统",
        description: "驴肉系统管理",
        unique: "唯一标识",
        roleInfo: "角色信息",
        role: "角色配置",
        selRole: "选择角色",
        setMenu: "设置菜单",
        selVCI: "选择一个产品设备",
        selPad: "选择一个Pad设备",
        padSn: "平板设备SN",
        vciSn: "VCI SN",
        bindVCI: "绑定VCI",
        bind: "解绑",
        setDefault: "设为默认",
        isDefault: "是否默认",
        userList: "用户列表",
        devList: "设备列表",
        full: "全屏",
        xuhao: "序号",
        title1: "标题",
        mc:"名称",
        title: "提示",
        resetPwd: "重置密码",
        unbindAccount:"客户账号解绑", 
        time: "当前时间：",
        home: "首页",
        road: "路径",
        order: "顺序",
        pageRoad: "页面路径",
        icon: "图标",
        search: "搜索",
        reset: "重置",
        add: "新增",
        addMenu: "新增子菜单",
        menuInfo: "菜单信息",
        edit: "编辑",
        action: "操作",
        cStatus: "已下架",
        cStatus: "已下架",
        bStatus: "发布状态",
        aStatus: "测试状态",
        down: "下架",
        downPro: "下载程序",
        publish: "发布",
        revise: "修改",
        view: "查看",
        del: "删除",
        url: "网址",
        toPush: "推送",
        DeleteInBulk: "批量删除",
        more: "更多操作",
        multiLang: "多语言描述",
        add1: "添加",
        selLang: "选择语言",
        uploadPro: "上传程序(*.apk文件)",
        uploadBin: "上传程序(*.bin文件)",
        uploadZPro: "上传诊断程序(zip文件)",
        uploadMenu: "上传菜单程序",
        uploadFlush: "上传刷写文件",
        selFile: "选择文件",
        toServer: "上传到服务器",
        uploading: "文件上传中...",
        uploadApp: "上传文件只支持 apk或apkx 格式！80M以内",
        uploadTips: "文件最多只能上传一个200M以内的文件",
        uploadWxzc: "上传文件只支持 pdf txt doc docx 格式！60M以内",
        uploadMenuTips: "支持 zip, db或bin 格式, 一个100M以内的文件",
        uploadFlushTips: "支持 .S19/.Bin/.HEX/.fp等类型文件格式, <br/>文件命名规则: 车型-控制器-类型(APP或Driver)-版本-日期-其它-校验码,如:G050_BMS_Driver_V1.02_74001779.hex",
        videoFormet: "支持 mp4 avi mov mkv flv wmv mpg mpeg 格式！",
        selVideo: "选择视频文件",
        video: "视频",
        status:"状态",
        lang: "语言",
        desc: "描述",
        createTime: "创建时间",
        recordTime: "记录时间",
        uploadTime: "上传时间",
        ok: "确定",
        cancel: "取消",
        url: "上传程序地址",
        version: "版本号",
        start: "开始日期",
        end: "结束日期",
        controlInfo: "控制器信息",
        clientNum: "客户端编号",
        clientName:"客户端名称",
        selClientNum:"请选择客户端编号",
        username: "用户名",
        eventType: "事件类型",
        eventDetails: "事件详情",
        eventLevel: "事件级别",
        ip: "IP地址",
        download: "下载",
        creator: "创建人",
        uploadPer: "请输入上传人",
        uploader: "上传人",
        fileSize: "文件大小(MB)",
        fileName: "文件名",
        upSet:"升级设置",
        pt:"普通升级",
        qz:"强制更新",
        appAdd: "应用软件新增",
        appvAdd: "软件版本新增",
        appvEdit: "软件版本编辑",
        pcbAdd: "包装规格新增",
        pcbVersion: "包装规格",
        selPcb: "请选择包装规格",
        pcbEdit: "包装规格编辑",
        sdkAdd: "配方版本新增",
        sdkEdit: "配方版本编辑",
        xm: "请输入用户姓名",
        userType: "驴肉管理员",
        header: {
            person: "个人信息",
            pass: "修改密码",
            logout: "注销"
        },
        pad: {
            con_client_no: "绑定的客户端编号",
            con_sn_no: "平板设备sn编号",
            con_account_no: "绑定的客户账号",
            client_no: "客户端编号",
            sn_no: "设备SN编号",
            account_no: "驴肉客户账号",
            add_pad: "新增平板设备",
            mod_pad: "修改平板设备",
        },
        program: {
            nav1: "诊断程序管理",
            nav2: "诊断程序",
            placeholder: "请输入测试版本操作人",
        },
        report: {
            nav1: "诊断报告管理",
            nav2: "诊断报告",
            num: "请输入编号",
            vin: "请输入VIN号",
            start: "开始日期",
            end: "结束日期",
            factory: "销售门店"
        },
        log: {
            nav1: "诊断日志管理",
            nav2: "诊断日志",
        },
        appv: {
            nav1: "产品质检管理",
            nav2: "软件版本",
            nav3: "包装规格",
            nav4: "配方版本",
            nav5: "香喷喷",
        },
        apps: {
            nav1: "应用软件管理",
            nav2: "PAD软件",
        },
        ziliao: {
            nav1: "驴肉资料管理",
            nav2: "维修手册"
        },
        sys: {
            nav1: "驴肉权限管理",
            nav2: "菜单配置",
            sysName: "请输入名称",
            delete: "批量删除",
            isDel: "确定要删除此菜单吗？",
        },
        brand:"品牌车系"
    },
    login: {
        title: "用户登录",
        account: "账  号",
        pass: "密  码",
        hitUser: "请输入用户名",
        hitPass: "请输入密码",
        btnName: "登 录",
        btnHit: "登 录中..."
    },
    employee: {
        nav1: "业务管理员",
        nav2: "员工管理",
        name: "请输入员工姓名",
        mobile: "请输入手机号",
        email: "请输入电子邮箱",
        id: "员工ID",
        nick: "员工姓名",
        status: "账号状态",
        role: "角色",
        add: "新增用户",
        edit: "编辑用户",
        email1: "员工邮箱",
        pwd: "登陆密码",
        mobile1: "员工手机"
    },
    menu: {
        nav1: "菜单版本管理",
        nav2: "菜单列表",
        cn: "中文菜单",
        en: "英文菜单",
        version: "请输入版本号",
        reg: "新增菜单登记",
        edit: "编辑菜单",
        cnDesc: "中文菜单描述",
        enDesc: "英文菜单描述",
    },
    repair: {
        name: "销售门店",
        hit1: "请输入修车厂名称",
        hit2: "请输入修车厂电话",
        title: "驴肉处理车间名称",
        phone: "修车厂电话",
        address: "修车厂地址",
        remark: "备注",
        reg: "销售门店登记",
        url: "销售门店网址",
        remark1: "额外备注",
        dit: "修改维修厂信息",
    },
    vci: {
        sys: "驴肉品质管理",
        dev: "驴肉加工设备",
        input: "请输入VCI编码",
        num: "VCI编号",
        ren: "领用人",
        code: "校验码",
        cell: "手机号",
        out: "出库人",
        blue: "VCI蓝牙地址",
        status: "状态(启用/禁用)",
        default: "默认是禁用状态",
        add: "新增产品设备",
        list: "用户列表",
    },
    ecu: {
       mgr: "ECU管理",
       rmgr: "驴肉配方管理",
       input: "请输入文件名",
       file: "新增刷写文件",
    },
    client: {
        mgr: "客户端管理",
        list: "客户端列表",
        name: "请输客户端名称",
        edit: "编辑客户端",
        add: "新增客户端",
        desc: "描述信息",
        desc1: "客户端描述",
        hit: "请输入6位数字",
    },
    band: {
        band_cn_name: "品牌中文名称",
        band_en_name: "品牌英文名称",
        band_mgr: "产品品牌管理",
        num: "编号",
        client_path: "客户端路径",
    },
    // 每个页面特有部分
    program: {
        diagnosisName: "诊断名称",
        versionNum: "版本号",
        operator: "测试版本操作人",
        bandName: "品牌名称",
        cnDesc: "中文描述",
        enDesc: "英文描述",
        downloadTip: "下载诊断程序",
        editProgram: "编辑诊断程序",
        addProgram: "新增诊断程序",
        selBand: "请选择品牌",
        clientNum:"客户端编号",
    },
    report: {
        id: "报告id",
        systemCount: "系统数",
        faultCount: "故障数",
        mileage: "里程(km)",
        repairNum: "报告编号",
        repairSn: "报告序列号",
        sn: "设备编号",
        vinNum: "VIN号",
    },
    log: {
        vciSn: "请输入设备vciSn号",
        account: "请输入用户名",
        dwPage: "下载本页",
    },
    ziliao: {
        clientBh: "请输入客户端编号",
        repairDesc: "请输入维修手册描述",
        editZiliao: "编辑维修资料",
        addZiliao: "维修手册新增",
        manualFile:"手册文件",
        manualRepl: "产品宣传视频替换",
        videoURL: "视频地址",
        noSupport: "您的浏览器不支持 HTML5 video 标签。",
        manualMgr: "驴肉烹饪手册",
        manual: "产品手册",
        videoMgr: "视频管理",
        videoDesc: "请输入视频描述",
        replVideo: "换视频文件",
        vTips: "视频最多只能上传一个50M以内的视频文件！",
        addVideo: "添加产品宣传视频",
        vUploading: "视频上传中...",
        manualDesc: "请输入产品手册描述",
        editManual: "编辑产品手册",
        addManual: "新增产品手册",
    },

}