<template>
  <el-menu
    class="min-h-full overflow-x-hidden bg-dark text-neutral shadow-elevation-1 transition-all duration-300"
    active-text-color="#888888"
    :collapse-transition="false"
    :collapse="isCollapse"
    :default-active="$route.path"
    router
  >
    <div class="h-16 flex items-center justify-center border-b border-gray-700">
      <img
        src="../assets/logo.svg"
        alt=""
        class="w-6 h-6 mr-2 transition-transform duration-300"
        :class="isCollapse ? 'transform scale-125' : ''"
      />
      <b class="text-black font-display text-lg transition-all duration-300" v-show="logoTextShow">{{
        $t("common.description")
      }}</b>
    </div>
    <div v-for="item in menus" :key="item.id">
      <div v-if="item.path">
        <el-menu-item :index="item.path">
          <component
            :is="item.icon"
            style="width: 18px; height: 18px; margin-right: 4px"
            :title="item.name"
          >
          </component>
          <template #title>{{ item.name }}</template>
        </el-menu-item>
      </div>
      <div v-else>
        <el-sub-menu :index="item.id + ''">
          <template #title>
            <component
              :is="item.icon"
              style="width: 18px; height: 18px; margin-right: 4px"
            />
            <span>{{ item.name }}</span>
          </template>
          <div v-for="subItem in item.children" :key="subItem.id">
            <el-menu-item :index="subItem.path">
              <component :is="subItem.icon" style="width: 18px; height: 18px" />
              <template #title>{{ subItem.name }}</template>
            </el-menu-item>
          </div>
        </el-sub-menu>
      </div>
    </div>
  </el-menu>
</template>

<script>
export default {
  name: "Aside",
  props: {
    isCollapse: Boolean,
    logoTextShow: Boolean,
  },
 
  data() {
    return {
      menus: localStorage.getItem("menus")
        ? JSON.parse(localStorage.getItem("menus"))
        : [],
      opens: localStorage.getItem("menus")
        ? JSON.parse(localStorage.getItem("menus")).map((v) => v.id + "")
        : [],
    };
  },
};
</script>

<style>
/*解决收缩菜单文字不消失问题*/
.el-menu--collapse span {
  visibility: hidden;
}
</style>
