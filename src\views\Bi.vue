<template>
    <div class="flex h-screen overflow-hidden">
      <!-- Content area -->
      <div class="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        <main>
          <div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
  
            <!-- Welcome banner -->
            <WelcomeBanner />
  
            <!-- Dashboard actions -->
            <div class="sm:flex sm:justify-between sm:items-center mb-8">
  
              <!-- Left: Avatars -->
              <DashboardAvatars />
  
              <!-- Right: Actions -->
              <div class="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
  
                <!-- Filter button -->
                <FilterButton align="right" />
                <!-- Datepicker built with flatpickr -->
                <Datepicker align="right" />
                <!-- Add view button -->
                <button class="btn bg-indigo-500 hover:bg-indigo-600 text-white">
                  <svg class="w-4 h-4 fill-current opacity-50 shrink-0" viewBox="0 0 16 16">
                    <path
                      d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
                  </svg>
                  <span class="hidden xs:block ml-2">Add view</span>
                </button>
              </div>
  
            </div>
  
            <!-- Cards -->
            <div class="grid grid-cols-12 gap-6">
  
              <!-- Line chart (Acme Plus) -->
              <DashboardCard01 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Line chart (Acme Advanced) -->
              <DashboardCard02 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Line chart (Acme Professional) -->
              <DashboardCard03 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Bar chart (Direct vs Indirect) -->
              <DashboardCard04 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Line chart (Real Time Value) -->
              <DashboardCard05 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Doughnut chart (Top Countries) -->
              <DashboardCard06 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Table (Top Channels) -->
              <DashboardCard07 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Line chart (Sales Over Time) -->
              <DashboardCard08 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Stacked bar chart (Sales VS Refunds) -->
              <DashboardCard09 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Card (Customers)  -->
              <DashboardCard10 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Card (Reasons for Refunds)   -->
              <DashboardCard11 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Card (Recent Activity) -->
              <DashboardCard12 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
              <!-- Card (Income/Expenses) -->
              <DashboardCard13 class="shadow-elevation-1 hover:shadow-elevation-2 transition-all duration-300 rounded-lg overflow-hidden"/>
  
            </div>
  
          </div>
        </main>
      </div>
    </div>
  </template>
  
  <script>
  import WelcomeBanner from '@/views/partials/dashboard/WelcomeBanner.vue'
  import DashboardAvatars from '@/views/partials/dashboard/DashboardAvatars.vue'
  import FilterButton from '@/views/components/DropdownFilter.vue'
  import Datepicker from '@/views/components/Datepicker.vue'
  import DashboardCard01 from '@/views/partials/dashboard/DashboardCard01.vue'
  import DashboardCard02 from '@/views/partials/dashboard/DashboardCard02.vue'
  import DashboardCard03 from '@/views/partials/dashboard/DashboardCard03.vue'
  import DashboardCard04 from '@/views/partials/dashboard/DashboardCard04.vue'
  import DashboardCard05 from '@/views/partials/dashboard/DashboardCard05.vue'
  import DashboardCard06 from '@/views/partials/dashboard/DashboardCard06.vue'
  import DashboardCard07 from '@/views/partials/dashboard/DashboardCard07.vue'
  import DashboardCard08 from '@/views/partials/dashboard/DashboardCard08.vue'
  import DashboardCard09 from '@/views/partials/dashboard/DashboardCard09.vue'
  import DashboardCard10 from '@/views/partials/dashboard/DashboardCard10.vue'
  import DashboardCard11 from '@/views/partials/dashboard/DashboardCard11.vue'
  import DashboardCard12 from '@/views/partials/dashboard/DashboardCard12.vue'
  import DashboardCard13 from '@/views/partials/dashboard/DashboardCard13.vue'
  
  export default {
    name: 'Bi',
    components: {
      WelcomeBanner,
      DashboardAvatars,
      FilterButton,
      Datepicker,
      DashboardCard01,
      DashboardCard02,
      DashboardCard03,
      DashboardCard04,
      DashboardCard05,
      DashboardCard06,
      DashboardCard07,
      DashboardCard08,
      DashboardCard09,
      DashboardCard10,
      DashboardCard11,
      DashboardCard12,
      DashboardCard13,
    }
  }
  </script>