<template>
  <div class="product-management">
    <div class="header-section">
      <h1 class="page-title">驴肉火烧库存管理</h1>
    </div>
    
    <div class="content-section">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>产品库存列表</span>
            <el-button type="primary" @click="handleAdd">添加产品</el-button>
          </div>
        </template>
        
        <el-table :data="productList" style="width: 100%" stripe>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="storageNumber" label="库存编号" width="120" />
          <el-table-column prop="productName" label="驴肉火烧名称" width="200" />
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column prop="stock" label="库存数量" width="120">
            <template #default="scope">
              <el-tag :type="getStockTagType(scope.row.stock)" size="small">
                {{ scope.row.stock }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button 
                type="success" 
                size="small" 
                @click="handleStock(scope.row)"
                :disabled="scope.row.stock <= 0"
              >
                上架
              </el-button>
              <el-button 
                type="primary" 
                size="small" 
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button 
                type="danger" 
                size="small" 
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="500px"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="库存编号" prop="storageNumber">
          <el-input v-model="form.storageNumber" placeholder="请输入库存编号" />
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入驴肉火烧名称" />
        </el-form-item>
        <el-form-item label="产品描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入产品描述"
          />
        </el-form-item>
        <el-form-item label="库存数量" prop="stock">
          <el-input-number 
            v-model="form.stock" 
            :min="0" 
            :max="999"
            placeholder="请输入库存数量"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const productList = ref([
  {
    id: 21,
    storageNumber: '8',
    productName: '青椒肉丝',
    description: '经典家常菜',
    stock: 28
  },
  {
    id: 20,
    storageNumber: '10',
    productName: '炸春卷',
    description: '外皮酥脆，内馅鲜美',
    stock: 15
  },
  {
    id: 19,
    storageNumber: '22',
    productName: '葱油拌面',
    description: '简单美味，香气扑鼻',
    stock: 32
  },
  {
    id: 18,
    storageNumber: '30',
    productName: '鱼香肉丝',
    description: '经典川菜，酸甜开胃',
    stock: 25
  }
])

const dialogVisible = ref(false)
const dialogTitle = ref('添加产品')
const formRef = ref()
const editingId = ref(null)

// 表单数据
const form = reactive({
  storageNumber: '',
  productName: '',
  description: '',
  stock: 0
})

// 表单验证规则
const rules = {
  storageNumber: [
    { required: true, message: '请输入库存编号', trigger: 'blur' }
  ],
  productName: [
    { required: true, message: '请输入产品名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入产品描述', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入库存数量', trigger: 'blur' }
  ]
}

// 方法
const getStockTagType = (stock) => {
  if (stock > 20) return 'success'
  if (stock > 10) return 'warning'
  return 'danger'
}

const handleAdd = () => {
  dialogTitle.value = '添加产品'
  dialogVisible.value = true
  editingId.value = null
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑产品'
  dialogVisible.value = true
  editingId.value = row.id
  
  // 填充表单数据
  form.storageNumber = row.storageNumber
  form.productName = row.productName
  form.description = row.description
  form.stock = row.stock
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除产品 "${row.productName}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = productList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      productList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const handleStock = (row) => {
  ElMessage.success(`产品 "${row.productName}" 已上架`)
}

const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (editingId.value) {
        // 编辑模式
        const index = productList.value.findIndex(item => item.id === editingId.value)
        if (index > -1) {
          productList.value[index] = {
            ...productList.value[index],
            ...form
          }
          ElMessage.success('编辑成功')
        }
      } else {
        // 添加模式
        const newProduct = {
          id: Date.now(), // 简单的ID生成
          ...form
        }
        productList.value.unshift(newProduct)
        ElMessage.success('添加成功')
      }
      
      dialogVisible.value = false
      resetForm()
    }
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.storageNumber = ''
  form.productName = ''
  form.description = ''
  form.stock = 0
  editingId.value = null
}

onMounted(() => {
  // 组件挂载后的初始化操作
})
</script>

<style scoped>
.product-management {
  padding: 20px;
}

.header-section {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  padding: 0 0 10px 0;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.content-section {
  background: #fff;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
