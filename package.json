{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.2", "chart.js": "^4.4.3", "chartjs-adapter-moment": "^1.0.1", "dayjs": "^1.11.11", "echarts": "^5.5.0", "element-plus": "^2.7.3", "pinia": "^2.1.7", "pinia-persistedstate-plugin": "^0.1.0", "screenfull": "^6.0.2", "vue": "^3.4.21", "vue-axios": "^3.5.2", "vue-flatpickr-component": "^11.0.5", "vue-i18n": "^9.13.1", "vue-router": "^4.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "sass": "^1.77.2", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "unplugin-auto-import": "^0.17.6", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.8"}}