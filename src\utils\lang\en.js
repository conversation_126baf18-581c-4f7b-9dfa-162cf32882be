export default {
    common: {
        welcome: "Welcome to system admin",
        description: "熟食商品运维后台管理系统",
        padSn: "Pad SN",
        vciSn: "VCI SN",
        role: "Role Mgr",
        roleInfo: "Role info",
        selRole: "Choose role",
        setMenu: "Setup menu",
        selVCI: "Select a VCI",
        unique: "Identifier",
        bind: "Unbind",
        setDefault: "Set Default",
        isDefault: "isDefault",
        selPad: "Select a Pad",
        bindVCI: "Bind with VCI",
        userList: "User List",
        devList: "Device List",
        full: "Full Screen",
        xuhao: "Sequence",
        title1: "Title",
        mc:"Name",
        title: "Tips",
        time: "Current Time:",
        search: "Search",
        reset: "Reset",
        home: "Home",
        resetPwd: "Reset Pwd",
        unbindAccount:"Unbind account", 
        road: "Route",
        order: "Order",
        pageRoad: "Page Route",
        icon: "Icon",
        add: "Add",
        addMenu: "Add SubMenu",
        menuInfo: "Menu Info",
        edit: "Edit",
        action: "Action",
        cStatus: "Unavailable",
        bStatus: "Published",
        aStatus: "Beta",
        down: "Go down",
        downPro: "Download Program",
        publish: "Publish",
        revise: "Revise",
        view: "View",
        del: "Delete",
        url: "Url",
        toPush: "Push",
        DeleteInBulk: "Delete in bulk",
        more: "More...",
        multiLang: "Description for multiLigual",
        add1: "Add",
        selLang: "Select Language",
        uploadPro: "Upload an apk program",
        uploadBin: "Upload a bin file",
        uploadZPro: "Upload a zip file)",
        uploadMenu: "Upload menu file",
        uploadFlush: "Upload flush file",
        selFile: "Select a file",
        toServer: "Upload to Server",
        uploading: "Uploading...",
        uploadApp: "Support apk or apkx formats！no more that 80M!",
        uploadTips: "One file once, no more than 200M",
        uploadWxzc: "Support pdf txt doc docx formats！no more than 60M",
        uploadMenuTips: "Support zip|db|bin formats, one file within 100M!",
        uploadFlushTips: "Support .S19/.Bin/.HEX/.fp formats, <br/>ß>naming rules: car type-controller-type(APP或Driver)-version-date-other-validaton code,ie:G050_BMS_Driver_V1.02_74001779.hex",
        videoFormet: "Support mp4 avi mov mkv flv wmv mpg mpeg formats！",
        selVideo: "Choose a video file",
        video: "Video",
        status:"Status",
        lang: "Language",
        desc: "Description",
        createTime: "Created at",
        recordTime: "Logging at",
        uploadTime: "Uploaded at",
        ok: "Apply",
        cancel: "Cancel",
        url: "URL of Program",
        version: "Version",
        start: "Start date",
        end: "End date",
        controlInfo: "Controller Info",
        clientNum: "Client Num",
        clientName:"Client Name",
        selClientNum:"Please Choose Client Code",
        username: "User Name",
        ip: "IP Adress",
        eventType: "Event Type",
        eventDetails: "Event Details",
        eventLevel: "Event Level",
        download: "Download",
        creator: "Creator",
        uploadPer: "Input Uploader",
        uploader: "Uploader",
        fileSize: "File Size(MB)",
        fileName: "File Name",
        upSet:"Upgrade Conf",
        pt:"Normal",
        qz:"Forced",
        appAdd: "Add an App",
        appvAdd: "Add new APP version",
        appvEdit: "Edit the APP version",
        pcbAdd: "Add new PCB version",
        pcbEdit: "Edit the PCB version",
        pcbVersion: "PCB Version",
        selPcb: "Choose PCB Version",
        sdkAdd: "Add new SDK version",
        sdkEdit: "Edit the SDK version",
        xm: "please input user name",
        userType: "User Type",
        header: {
            person: "Personal info",
            pass: "Modify pass",
            logout: "Log out"
        },
        pad: {
            con_client_no: "Binded client number",
            con_sn_no: "Pad sn number",
            con_account_no: "Binded client account",
            client_no: "Client number",
            sn_no: "Device SN number",
            account_no: "Client account",
            add_pad: "Add new pad",
            mod_pad: "Modify the pad",
        },
        program: {
            nav1: "Diagnostics management",
            nav2: "Diagnostics",
            placeholder: "Please enter operator",
        },
        report: {
            nav1: "Diagnostic report management",
            nav2: "Diagnostic report",
            num: "Input Code",
            vin: "Input VIN number",
            start: "Start date",
            end: "End date",
            factory: "Garage"
        },
        log: {
            nav1: "Diagnostic logging management",
            nav2: "Diagnostic logging",
        },
        appv: {
            nav1: "VCI Mgr",
            nav2: "APP version",
            nav3: "PCB version",
            nav4: "SDK version",
            nav5: "PAD Mgr",
        },
        apps: {
            nav1: "Apps Management",
            nav2: "PAD",
        },
        ziliao: {
            nav1: "Repair Manual Mgr",
            nav2: "Repair Manual"
        },
        sys: {
            nav1: "Permission Mgr",
            nav2: "Menu Mgr",
            sysName: "Input name",
            delete: "Batch Delete",
            isDel: "Sure to delete this menu?",
        },
        brand:"brand"
    },
    band: {
        band_cn_name: "Band name(cn)",
        band_en_name: "Band name(en)",
        band_mgr: "Band mgr",
        num: "Number",
        client_path: "Client path",
    },
    login: {
        title: "Welcome to Login",
        account: "User name",
        pass: "password",
        hitUser: "please enter user name",
        hitPass: "please enter password",
        btnName: "Sign in",
        btnHit: "Sign in..."
    },
    employee: {
        nav1: "Sys Admin",
        nav2: "Employee mgr",
        name: "please input employee name",
        mobile: "please input mobile number",
        email: "please input email address",
        id: "Empployee ID",
        nick: "Employee name",
        status: "Status",
        role: "Role",
        add: "Add a new user",
        edit: "Edit the user",
        email1: "Email",
        pwd: "password",
        mobile1: "mobile"
    },
    menu: {
        nav1: "Menu version mgr",
        nav2: "Menu list",
        cn: "Chinese menu",
        en: "English menu",
        version: "please input version",
        reg: "Add a new menu",
        edit: "Edit the menu",
        cnDesc: "Description of CN menu",
        enDesc: "Description of EN menu",
    },
    repair: {
        name: "Repair factory",
        hit1: "please enter repair factory name",
        hit2: "please enter phone number",
        title: "Repair factory name",
        phone: "Phone number",
        address: "Repair factory address",
        remark: "Remarks",
        reg: "Register new repair factory",
        url: "URL",
        remark1: "Additional remarks",
        edit: "Revise factory info",
    },
    vci: {
        sys: "Sys admin",
        dev: "VCI device",
        input: "please enter vci number",
        num: "VCI number",
        ren: "Usage Person",
        code: "Code",
        cell: "Cell Phone",
        out: "Outer",
        blue: "VCI bluetooth address",
        status: "Status(enable/disable)",
        default: "Disabled in default",
        add: "Add new VCI device",
        list: "User list",
    },
    ecu: {
        mgr: "ECU mgr",
        rmgr: "ECU refresh file",
        input: "please enter filename",
        file: "Add refresh files",
     },
    client: {
        mgr: "Client-side mgr",
        list: "Client-side list",
        name: "Please input client-side name",
        edit: "Edit Client-side",
        add: "Add Client-side",
        desc: "Description",
        desc1: "Client-side desc",
        hit: "please enter 6-bit digtal",
    },
    program: {
        diagnosisName: "Diagnosis Name",
        versionNum: "Version",
        operator: "Operator",
        bandName: "Band Name",
        cnDesc: "Chinese Description",
        enDesc: "English Description",
        downloadTip: "Download Diagnostics",
        editProgram: "Revise the Diagnostics",
        addProgram: "Add a Diagnostics",
        selBand: "Select Band",
        clientNum:"Client Code",
    },
    report: {
        id: "Reprot id",
        systemCount: "Nums of System",
        faultCount: "defects",
        mileage: "Mileage(km)",
        repairNum: "Report Code",
        repairSn: "Report SN",
        sn: "Device SN",
        vinNum: "VIN",
    },
    log: {
        vciSn: "Input device vciSn",
        account: "Input user name",
        dwPage: "Download page",
    },
    ziliao: {
        clientBh: "Input code of client side",
        repairDesc: "Input description of repair manual",
        editZiliao: "Edit repair doc",
        addZiliao: "Add repair doc",
        manualFile:"Manual File",
        manualRepl: "Replace video",
        videoURL: "Video URL",
        noSupport: "Your browser no support for HTML5 video!",
        manualMgr: "Manual Mgr",
        manual: "Manual",
        videoMgr: "Video Mgr",
        videoDesc: "Input description of the video",
        replVideo: "Replace video",
        vTips: "A video with noe more than 50M！",
        addVideo: "Add video for manual",
        vUploading: "Video uploading ...",
        manualDesc: "Input description of the Manual",
        editManual: "Edit manual",
        addManual: "Add manual",
    }

}