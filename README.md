### 安装依赖库

```sh
npm install primeflex primeicons primevue chart.js
```

#### 安装持久化依赖插件

```shell
npm install pinia-persistedstate-plugin
```

#### 配置Pinia
在 main.js中配置
```js
// 持久化状态
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-persistedstate-plugin'

// 使用pinia持久化
const pinia = createPinia()
const persist = createPersistedState()
pinia.use(persist)
app.use(pinia)

```

#### 编写 userInfo.js
在 stores 目录下，新建 userInfo.js，并编写如下代码：

```js
import { defineStore } from "pinia";
import { ref } from "vue";
const useUserInfoStore = defineStore(
  "userInfo",
  () => {
    //定义状态相关的内容

    const info = ref({});

    const setInfo = (newInfo) => {
      info.value = newInfo;
    };

    const removeInfo = () => {
      info.value = {};
    };

    return { info, setInfo, removeInfo };
  },
  { persist: true }
);

export default useUserInfoStore;

```


#### 获取并存储用户信息
在 Layout.vue中

```js
getUser() {
  //从后台获取数据
  this.request.get("/user/userInfo").then(res => {
    // 增加无Token时的判断和跳转
    if (res.code === 401) {
      ElMessage({
        showClose: true,
        message: res.msg,
        type: "error",
        duration: 1000
      })
      this.$router.push("/login")
    } else {
      // 存储用户信息
      const userInfoStore = useUserInfoStore()
      userInfoStore.setInfo(res.data)
      this.employee = res.data
    }
  }).catch(e => {
    ElMessage({
      showClose: true,
      type: "error",
      message: e,
    })
    this.$router.push("/login")
  })
}
```
#### 在Home.vue展示用户信息并修改

```js
<script setup>
import { ref } from 'vue'
import useUserInfoStore from '@/stores/userInfo.js'
const userInfoStore = useUserInfoStore();

const userInfo = ref({...userInfoStore.info})

const rules = {
    nickname: [
        { required: true, message: '请输入用户昵称', trigger: 'blur' },
        {
            pattern: /^\S{2,10}$/,
            message: '昵称必须是2-10位的非空字符串',
            trigger: 'blur'
        }
    ],
    email: [
        { required: true, message: '请输入用户邮箱', trigger: 'blur' },
        { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
    ]
}

//修改个人信息
// import {userInfoUpdateService} from '@/api/user.js'
import {ElMessage} from 'element-plus'
const updateUserInfo = async ()=>{
    //调用接口
    // let result = await userInfoUpdateService(userInfo.value);
    // ElMessage.success(result.msg? result.msg : '修改成功');
    
    // //修改pinia中的个人信息
    // userInfoStore.setInfo(userInfo.value)
}
</script>
<template>
    <el-card class="page-container">
        <template #header>
            <div class="header">
                <span>基本资料</span>
            </div>
        </template>
        <el-row>
            <el-col :span="12">
                <el-form :model="userInfo" :rules="rules" label-width="100px" size="large">
                    <el-form-item label="登录名称">
                        <el-input v-model="userInfo.username" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="用户昵称" prop="nickname">
                        <el-input v-model="userInfo.nickname"></el-input>
                    </el-form-item>
                    <el-form-item label="用户邮箱" prop="email">
                        <el-input v-model="userInfo.email"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="updateUserInfo">提交修改</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
    </el-card>
</template>
```

#### 修改路由表
```js

```