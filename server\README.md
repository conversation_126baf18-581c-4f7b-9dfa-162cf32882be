# Vue Project Server

This is a simple Express server implementation to handle authentication for the Vue project.

## Features

- Login authentication
- JWT token generation
- Mock user data and menus
- API endpoints for user validation

## Setup

1. Install dependencies:

```bash
npm install
```

2. Start the server:

```bash
npm start
```

## API Endpoints

- `POST /web/employee/login` - Login authentication
- `GET /web/employee/username/:username` - Get user by username

## Configuration

Edit the `index.js` file to modify:

- Port (default: 3000)
- JWT secret key
- Mock user data
- Menu structure

## Note

This is a development server for testing purposes. In a production environment, you would:

- Use environment variables for secrets
- Implement proper database storage
- Add more robust error handling
- Implement proper password hashing
- Add additional security measures
